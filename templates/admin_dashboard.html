<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - Roulette Tracker</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.4/socket.io.min.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body class="admin-dashboard-body">
    <div class="admin-container">
        <header class="dashboard-header">
            <div>
                <h1>Roulette Tracker Dashboard</h1>
                <p>Welcome, <strong id="admin-username-display">{{ username }}</strong>! (Session: <span id="admin-session-id-display">{{ session_id[:8] }}...</span>)</p>
                <p>Connected Users: <span id="connected-users-count">0</span></p>
                <p>Current Wheel: <span id="current-wheel-type-display" class="status-pill status-neutral">N/A</span></p>
            </div>
            <button id="logout-button" class="button-danger">Logout</button>
        </header>

        <!-- Mobile-friendly focused view with key elements -->
        <div class="dashboard-focused-view">
            <!-- Compact Tracker Status -->
            <section class="status-panel card">
                <h2>Tracker Status</h2>
                <div class="status-grid">
                    <div class="status-item status-item-compact">
                        <span id="status-indicator" class="status-pill">[N/A]</span>
                        <span id="entry-count" class="status-text">Count: N/A</span>
                        <span id="last-spin-display" class="status-text">Last: N/A</span>
                    </div>
                    <div class="status-item status-item-compact">
                        <span id="last-match-display" class="status-text">Since Match: N/A</span>
                        <span id="max-win-streak" class="status-text">Max Wins: N/A</span>
                        <span id="max-loss-streak" class="status-text">Max Losses: N/A</span>
                    </div>
                    <div class="status-item status-item-compact">
                        <span id="avg-spins-win" class="status-text">Avg/Win: N/A</span>
                        <span id="save-target" class="status-text">Save: N/A</span>
                    </div>
                </div>
            </section>

            <!-- Current Distill Output (now before Previous) -->
            <section class="output-panel card current-distill-panel">
                <h3>Current Distill Output</h3>
                <pre id="distill-output">(No data yet)</pre>
            </section>

            <!-- Audio Controls -->
            <section class="tts-controls card">
                <h3>Speech Controls</h3>
                <div class="tts-button-group">
                    <button id="tts-stop-button" class="button-tts">Stop</button>
                    <button id="tts-mute-button" class="button-tts">Mute</button>
                    <button id="tts-repeat-button" class="button-tts">Repeat</button>
                    <button id="tts-slower-button" class="button-tts">&lt; Slower</button>
                    <span id="tts-rate-display">Rate: 1.0</span>
                    <button id="tts-faster-button" class="button-tts">Faster &gt;</button>
                </div>
                <div class="tts-select-group">
                    <label for="tts-repetitions-select">Repeat:</label>
                    <select id="tts-repetitions-select">
                        <option value="1" selected>1 time</option>
                        <option value="2">2 times</option>
                        <option value="3">3 times</option>
                    </select>
                </div>
            </section>

            <!-- Previous Distill Output (now after Current) -->
            <section class="output-panel card previous-distill-panel">
                <h3>Previous Distill Output</h3>
                <pre id="previous-distill-output">(No data yet)</pre>
            </section>
        </div>
        <div id="admin-message-area" class="message-box" style="margin-top: 15px; margin-bottom:15px; display:none;"></div>


        <div class="dashboard-main-grid">
            <div class="dashboard-column-left">
                <section class="control-panel card">
                    <h2>Session Controls</h2>
                    <button id="generate-code-button" class="button-primary">Generate User Code</button>
                    <div class="code-display-container">
                        <div id="code-display-area" class="message-box info-box" style="display:none;"></div>
                        <button id="copy-code-button" class="button-secondary small-button" style="display:none;">Copy Code</button>
                    </div>

                    <div class="input-group">
                        <label for="spin-input">Enter Spin(s) or File Base:</label>
                        <input type="text" id="spin-input" placeholder="e.g., 10 or M-DealerName">
                    </div>

                    <div class="button-group">
                        <button id="submit-spin-button" class="button-success">Submit Spin(s)</button>
                        <button id="undo-button" class="button-warning">Undo Last</button>
                        <button id="save-session-button" class="button-primary">Save Session</button>
                        <button id="reset-button" class="button-danger">Reset Session</button>
                        <button id="change-file-button" class="button-secondary">Use Entry as File & Reset</button>
                    </div>
                </section>

                <section class="wheel-config-panel card">
                    <h2>Wheel Configuration</h2>
                    <p>Select roulette wheel type. This will reset the current session data.</p>
                    <div class="input-group">
                        <label for="wheel-type-select">Wheel Type:</label>
                        <select id="wheel-type-select">
                            <option value="american">American (0, 00)</option>
                            <option value="european">European (0)</option>
                        </select>
                    </div>
                    <button id="change-wheel-type-button" class="button-primary">Apply & Reset Session</button>
                </section>
            </div>

            <div class="dashboard-column-right">
                <section class="output-display card">
                    <h2>Spin History (<span id="spin-history-count-display">0</span>)</h2>
                    <ul id="spin-history-list" class="spin-list">
                        <li>No spins yet.</li>
                    </ul>
                </section>

                <section class="history-management-panel card">
                    <h2>Session History Management</h2>
                    <div class="history-controls">
                        <button id="refresh-history-list-button" class="button-secondary small-button">Refresh List</button>
                    </div>
                    <div class="input-group">
                        <label for="saved-history-select">Saved Histories:</label>
                        <select id="saved-history-select" style="margin-bottom: 10px;">
                            <option value="">-- Select a history file --</option>
                        </select>
                         <div class="button-group" style="justify-content: flex-start;">
                            <button id="view-history-button" class="button-primary small-button" disabled>View Selected</button>
                            <button id="delete-history-button" class="button-danger small-button" disabled>Delete Selected</button>
                        </div>
                    </div>
                    <div id="history-view-area-container">
                        <h3>Viewing: <span id="viewing-history-filename"></span></h3>
                        <pre id="history-view-area">(Select a history to view its content)</pre>
                    </div>
                    <div id="history-management-message-area" class="message-box" style="margin-top:10px; display:none;"></div>
                </section>
            </div>
        </div>
    </div>
    <script>
        // Pass session data from Flask template to JavaScript
        const ADMIN_SESSION_ID = "{{ session_id }}";
        const ADMIN_USERNAME = "{{ username }}";
    </script>
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
</body>
</html>

# --- File: app.py ---
import eventlet
eventlet.monkey_patch()

from flask import Flask, request, jsonify, render_template, session, redirect, url_for, send_from_directory
from flask_socketio import SocketIO, emit, join_room, leave_room
import os
import random
import string
from collections import deque
import logging
import copy
from datetime import datetime, timedelta, timezone
import secrets
from werkzeug.security import generate_password_hash, check_password_hash
import time
import threading
import json
import re # For sanitizing filenames

# --- Custom Audit Logger ---
from audit_logger import log_event

# --- Flask App Setup ---
app = Flask(__name__)
app.config['SECRET_KEY'] = secrets.token_hex(32)
app.config['SESSION_COOKIE_SAMESITE'] = "Lax"
socketio = SocketIO(app, cors_allowed_origins="*", async_mode='eventlet')

# --- Configuration ---
LOGS_DIRECTORY = "logs"
SESSION_HISTORY_DIRECTORY = "session_history"
os.makedirs(LOGS_DIRECTORY, exist_ok=True)
os.makedirs(SESSION_HISTORY_DIRECTORY, exist_ok=True)

RED_THRESHOLD = 70
YELLOW_THRESHOLD = 90
DEFAULT_SAVE_FILE_PREFIX = "roulette_spins_session" # This prefix is mainly for the display name now

SESSION_HISTORY_METADATA_FILE = os.path.join(SESSION_HISTORY_DIRECTORY, "session_history_metadata.json")
MAX_SAVED_SESSIONS_METADATA = 100
SESSION_INACTIVITY_TIMEOUT = timedelta(hours=2)
ONE_TIME_CODE_VALIDITY = timedelta(minutes=10)

rate_limit_store = {}
LOGIN_ATTEMPT_LIMIT = 5
LOGIN_ATTEMPT_WINDOW = timedelta(minutes=5)
CODE_GEN_LIMIT = 10
CODE_GEN_WINDOW = timedelta(minutes=1)

admin_users = {
    "scharway": {"hashed_password": generate_password_hash("Lookup88?")} # IMPORTANT: Change in production!
}
active_sessions = {}
one_time_codes = {}

# --- Core Logic (RouletteTracker) ---
class RouletteTracker:
    def __init__(self, wheel_type='american'): # Default to American
        self.wheel_type = wheel_type
        self.american_wheel_sequence = [
            '0', '28', '9', '26', '30', '11', '7', '20', '32', '17', '5', '22', '34', '15', '3', '24', '36', '13', '1',
            '00', '27', '10', '25', '29', '12', '8', '19', '31', '18', '6', '21', '33', '16', '4', '23', '35', '14', '2'
        ]
        self.european_wheel_sequence = [
            '0', '32', '15', '19', '4', '21', '2', '25', '17', '34', '6',
            '27', '13', '36', '11', '30', '8', '23', '10', '5', '24', '16',
            '33', '1', '20', '14', '31', '9', '22', '18', '29', '7', '28',
            '12', '35', '3', '26'
        ]

        if self.wheel_type == 'american':
            self.wheel_sequence = self.american_wheel_sequence
            self.num_slots = 38
        else: # european
            self.wheel_sequence = self.european_wheel_sequence
            self.num_slots = 37

        self.valid_numbers = set(self.wheel_sequence)
        self.spin_distances = deque(range(self.num_slots))
        self.last_spin = None # Renaming this internal var is a larger change, kept as 'last_spin' for now. User-facing text is what matters.
        self.history = []

    def update_spin(self, spin_number_str): # 'spin' here is conceptual for the number entered
        # Check if input contains hyphens, which indicates multiple numbers
        if '-' in spin_number_str:
            # Process each number separated by hyphens
            numbers = [num.strip() for num in spin_number_str.split('-')]
            for num in numbers:
                # Recursively call update_spin for each number
                self.update_spin(num)
            return True

        spin_number = str(spin_number_str).strip()
        if not spin_number:
            raise ValueError("Number cannot be empty.")
        if spin_number not in self.valid_numbers:
            valid_sorted = sorted(list(self.valid_numbers), key=lambda x: (x not in ['0', '00'], int(x) if x.isdigit() else (0.5 if x == '00' else float('inf'))))
            raise ValueError(f"Invalid number: '{spin_number}'. Valid for {self.wheel_type.capitalize()} wheel: {'-'.join(valid_sorted)}")

        self.history.append(spin_number)
        if self.last_spin is not None:
            try:
                last_index = self.wheel_sequence.index(self.last_spin)
                current_index = self.wheel_sequence.index(spin_number)
                distance = (current_index - last_index + self.num_slots) % self.num_slots
                if distance in self.spin_distances:
                    self.spin_distances.remove(distance)
                self.spin_distances.appendleft(distance)
            except ValueError as e:
                logging.error(f"Error finding index for number: {e}. Last: {self.last_spin}, Current: {spin_number}")
                self.history.pop() # Rollback history append
                raise ValueError("Internal error processing number indices.") from e
        self.last_spin = spin_number
        return True

    def undo_last_spin(self): # "spin" here is internal, refers to last entry
        if not self.history: return False
        self.history.pop()
        temp_history_for_rebuild = list(self.history)
        self._reset_internal_state_for_rebuild()

        self.history = []
        try:
            for number_entry in temp_history_for_rebuild:
                _temp_last_entry = self.last_spin # Use current self.last_spin before it's updated by adding 'number_entry'

                self.history.append(number_entry) # Add to internal history

                if _temp_last_entry is not None:
                    last_idx_rebuild = self.wheel_sequence.index(_temp_last_entry)
                    current_idx_rebuild = self.wheel_sequence.index(number_entry)
                    dist_rebuild = (current_idx_rebuild - last_idx_rebuild + self.num_slots) % self.num_slots
                    if dist_rebuild in self.spin_distances:
                        self.spin_distances.remove(dist_rebuild)
                    self.spin_distances.appendleft(dist_rebuild)
                self.last_spin = number_entry # Update last_spin for the next iteration
        except ValueError as e:
            logging.error(f"Error rebuilding state after undo: {e}")
            self._reset_internal_state_for_rebuild()
            self.history = []
            return False

        if not self.history:
            self.last_spin = None
            self.spin_distances = deque(range(self.num_slots))
        return True


    def _reset_internal_state_for_rebuild(self):
        self.spin_distances = deque(range(self.num_slots))
        self.last_spin = None

    def reset_tracker_and_history(self):
        self._reset_internal_state_for_rebuild()
        self.history = []

    def entry_count(self): return len(self.history)

    def get_unhit_numbers(self):
        if self.last_spin is None: return {}, [], []
        recent_distances = list(self.spin_distances)[:min(3, len(self.spin_distances))]
        oldest_distances = list(self.spin_distances)[-min(1, len(self.spin_distances)):] if len(self.spin_distances) > 0 else []
        try: last_spin_index = self.wheel_sequence.index(self.last_spin)
        except ValueError:
            logging.error(f"Last number '{self.last_spin}' not found in get_unhit_numbers.")
            return {}, [], []

        newest_numbers = [self.wheel_sequence[(last_spin_index + d + self.num_slots) % self.num_slots] for d in recent_distances]
        oldest_numbers = [self.wheel_sequence[(last_spin_index + d + self.num_slots) % self.num_slots] for d in oldest_distances]

        unhit_set = set(newest_numbers + oldest_numbers)
        categorized = {'Zs': [], 'D1': [], 'D2': [], 'D3': []}
        for num_str in unhit_set:
            if num_str in ['0', '00']: categorized['Zs'].append(num_str)
            else:
                try:
                    n = int(num_str)
                    if 1 <= n <= 12: categorized['D1'].append(num_str)
                    elif 13 <= n <= 24: categorized['D2'].append(num_str)
                    elif 25 <= n <= 36: categorized['D3'].append(num_str)
                except ValueError: logging.warning(f"Could not convert unhit '{num_str}' to int.")
        for cat in categorized:
            categorized[cat].sort(key=lambda x: (x not in ['0', '00'], int(x) if x.isdigit() else (0.5 if x == '00' else -1)))
        return categorized, newest_numbers, oldest_numbers

# --- Helper Functions (Original App Adapted) ---
def get_status_indicator(count):
    if count <= RED_THRESHOLD: return "[RED]"
    if count <= YELLOW_THRESHOLD: return "[YELLOW]"
    return "[GREEN]"

def compute_greedy_hitting_set(spins_list_str, randomize=True): # Internal name 'spins' fine
    if not spins_list_str or len(spins_list_str) < 2: return set()
    spins = [str(s) for s in spins_list_str]
    duplexes = [tuple(spins[i:i+2]) for i in range(len(spins) - 1)]
    n_duplexes = len(duplexes)
    if n_duplexes == 0: return set()

    unique_spins = set(spins)
    spin_to_duplexes = {spin: set() for spin in unique_spins}
    for idx, duplex in enumerate(duplexes):
        for spin_in_duplex in set(duplex):
            if spin_in_duplex in spin_to_duplexes: spin_to_duplexes[spin_in_duplex].add(idx)

    spin_freq = {s: spins.count(s) for s in unique_spins}
    duplex_cover_ct = [sum(1 for s_val in spin_to_duplexes if idx in spin_to_duplexes[s_val]) for idx in range(n_duplexes)]

    spin_uniq_score = {
        s: sum(1.0 / max(1, duplex_cover_ct[idx]) for idx in spin_to_duplexes.get(s, set()) if 0 <= idx < n_duplexes)
        for s in unique_spins
    }

    uncovered_indices = set(range(n_duplexes))
    hitting_s = set()
    candidates = list(unique_spins)

    while uncovered_indices and candidates:
        best_this_round, max_cov = [], -1
        for s_cand in candidates:
            cov = len(uncovered_indices & spin_to_duplexes.get(s_cand, set()))
            if cov > max_cov: max_cov, best_this_round = cov, [s_cand]
            elif cov == max_cov and cov > 0: best_this_round.append(s_cand)

        if not best_this_round: break
        chosen = ""
        if len(best_this_round) == 1: chosen = best_this_round[0]
        else:
            if randomize:
                weights = [spin_freq.get(s,1) * spin_uniq_score.get(s,0.1) + 0.1 for s in best_this_round]
                try: chosen = random.choices(best_this_round, weights=weights, k=1)[0]
                except IndexError: chosen = random.choice(best_this_round) if best_this_round else ""
            else: chosen = max(best_this_round, key=lambda s: (spin_freq.get(s,0), spin_uniq_score.get(s,0)))

        if not chosen: break
        hitting_s.add(chosen)
        uncovered_indices -= spin_to_duplexes.get(chosen, set())
        candidates.remove(chosen)
    return hitting_s

def compute_best_greedy_hitting_set(spins_list_str, runs=1300): # Internal name 'spins' fine
    if not spins_list_str or len(spins_list_str) < 3: return set()
    best_s = compute_greedy_hitting_set(spins_list_str, randomize=False)
    if best_s is None: best_s = set()
    min_sz = len(best_s)
    for _ in range(runs -1):
        curr_s = compute_greedy_hitting_set(spins_list_str, randomize=True)
        if curr_s is None: continue
        if len(curr_s) < min_sz: min_sz, best_s = len(curr_s), curr_s
        elif len(curr_s) == min_sz and sorted(list(curr_s), key=lambda x: (x not in ['0','00'], str(x))) < sorted(list(best_s), key=lambda x: (x not in ['0','00'], str(x))):
             best_s = curr_s
    return best_s

def categorize_numbers_for_display(numbers_set):
    cats = {'Zs': [], 'D1': [], 'D2': [], 'D3': []}
    if not numbers_set: return cats
    sorted_nums = sorted(list(numbers_set), key=lambda x: (x not in ['0', '00'], str(x) if x not in ['0', '00'] else ('0' if x == '0' else '00A')))
    for num_str in sorted_nums:
        if num_str in ["0", "00"]: cats['Zs'].append(num_str)
        else:
            try:
                n = int(num_str)
                if 1 <= n <= 12: cats['D1'].append(num_str)
                elif 13 <= n <= 24: cats['D2'].append(num_str)
                elif 25 <= n <= 36: cats['D3'].append(num_str)
            except ValueError: logging.warning(f"Categorize: Could not convert '{num_str}' to int.")
    return cats


def calculate_distilled_output(tracker_unhit_cat, hitting_set_cat):
    distilled = {'Zs': [], 'D1': [], 'D2': [], 'D3': []}
    if not hitting_set_cat: return distilled, 0
    tracker_seen = {num for cat_nums in tracker_unhit_cat.values() for num in cat_nums}
    dist_count = 0
    for cat_key, hs_nums in hitting_set_cat.items():
        dist_cat_nums = [num for num in hs_nums if num not in tracker_seen]
        distilled[cat_key] = dist_cat_nums
        dist_count += len(dist_cat_nums)
    return distilled, dist_count

# --- Utility Functions (New/Adapted) ---
def get_request_details(req): return {'path': req.path, 'method': req.method, 'user_agent': str(req.user_agent)}

def check_rate_limit(ip, action, limit, window):
    now = datetime.now(timezone.utc)
    if ip not in rate_limit_store: rate_limit_store[ip] = {}
    if action not in rate_limit_store[ip]:
        rate_limit_store[ip][action] = [now, 1]; return True
    last_t, ct = rate_limit_store[ip][action]
    if now - last_t > window: rate_limit_store[ip][action] = [now, 1]; return True
    if ct < limit: rate_limit_store[ip][action][1] += 1; return True
    return False

def generate_one_time_code_value(): return ''.join(random.choices(string.ascii_uppercase + string.digits, k=8))

def get_session_if_valid(s_id):
    s_data = active_sessions.get(s_id)
    if s_data:
        if datetime.now(timezone.utc) - s_data['last_activity'] < SESSION_INACTIVITY_TIMEOUT:
            s_data['last_activity'] = datetime.now(timezone.utc); return s_data
        else: cleanup_session(s_id, reason="timeout_get_session"); return None
    return None

def _load_session_metadata():
    if os.path.exists(SESSION_HISTORY_METADATA_FILE):
        try:
            with open(SESSION_HISTORY_METADATA_FILE, 'r', encoding='utf-8') as mf:
                data = json.load(mf)
                if isinstance(data, dict) and 'sessions' in data and isinstance(data['sessions'], list):
                    return data
                else: # Older format or corrupted, try to salvage if it's just a list
                    if isinstance(data, list): return {'sessions': data}
                    return {'sessions': []}
        except json.JSONDecodeError:
            return {'sessions': []}
    return {'sessions': []}

def _save_session_metadata(meta):
    try:
        with open(SESSION_HISTORY_METADATA_FILE, 'w', encoding='utf-8') as mf:
            json.dump(meta, mf, indent=2)
    except Exception as e:
        app.logger.error(f"Error saving session metadata: {e}")

def save_admin_session_history_file(admin_user, number_history_list, s_id_filename, wheel_type_info="unknown_wheel"):
    if not number_history_list: return
    ts_obj = datetime.now(timezone.utc)
    ts_str_filename = ts_obj.strftime("%Y%m%d_%H%M%S_%f") # Added microseconds for more uniqueness
    ts_str_metadata = ts_obj.isoformat() # Store ISO format in metadata for easier parsing

    safe_s_id_part = re.sub(r'[^a-zA-Z0-9_-]', '', s_id_filename[:8])
    safe_admin_user = re.sub(r'[^a-zA-Z0-9_-]', '', admin_user)

    # Filename uses the specific timestamp string including microseconds
    fname = os.path.join(SESSION_HISTORY_DIRECTORY, f"session_{safe_admin_user}_{ts_str_filename}_{safe_s_id_part}.txt")
    try:
        with open(fname, 'w', encoding='utf-8') as f: f.write("-".join(map(str, number_history_list)))

        meta = _load_session_metadata()
        if 'sessions' not in meta or not isinstance(meta['sessions'], list): # Ensure 'sessions' key exists and is a list
             meta['sessions'] = []

        meta['sessions'].append({
            "id": s_id_filename,
            "admin": admin_user,
            "timestamp": ts_str_metadata, # ISO format for metadata
            "filename": os.path.basename(fname), # This is the unique ID for the file
            "spin_count": len(number_history_list), # Retained name 'spin_count' for compatibility if frontend uses it
            "wheel_type": wheel_type_info
        })
        meta['sessions'] = sorted(meta['sessions'], key=lambda x: x['timestamp'], reverse=True)[:MAX_SAVED_SESSIONS_METADATA]
        _save_session_metadata(meta)
        log_event("admin.save_history", admin_user, "N/A", "success", details={"file": fname, "s_id": s_id_filename, "wheel": wheel_type_info})
    except Exception as e:
        app.logger.error(f"Error saving history for {admin_user} (session {s_id_filename}): {e}")
        log_event("admin.save_history", admin_user, "N/A", "failure", details={"err": str(e), "s_id": s_id_filename})


def cleanup_session(s_id, reason="unknown"):
    if s_id in active_sessions:
        s_data_clean = active_sessions.pop(s_id)
        admin_user = s_data_clean.get('username', 'unknown_admin')
        current_wheel_type = s_data_clean.get('wheel_type', 'unknown')
        if s_data_clean.get("role") == "admin" and "tracker_instance" in s_data_clean:
            tracker = s_data_clean["tracker_instance"]
            if hasattr(tracker, "history") and tracker.history:
                 save_admin_session_history_file(admin_user, tracker.history, s_id, current_wheel_type)
        log_event(f"system.session_cleanup", admin_user, "N/A", "success", details={"s_id": s_id, "reason": reason})
        app.logger.info(f"Session {s_id} for {admin_user} cleaned: {reason}.")
        associated_codes = [code for code, data in one_time_codes.items() if data['session_id'] == s_id]
        for code in associated_codes: one_time_codes.pop(code, None); app.logger.info(f"OTC {code} for session {s_id} removed.")

def periodic_cleanup_task():
    app.logger.info("Periodic cleanup thread started.")
    while True:
        time.sleep(60 * 15)
        now = datetime.now(timezone.utc)
        app.logger.debug(f"Running periodic cleanup at {now.isoformat()}...")
        exp_s_ids = [sid for sid, sdata in list(active_sessions.items()) if now - sdata.get('last_activity', now) > SESSION_INACTIVITY_TIMEOUT]
        for sid in exp_s_ids: cleanup_session(sid, reason="periodic_timeout")
        exp_codes = [c for c,cdata in list(one_time_codes.items()) if cdata.get('used') or cdata.get('expires_at', now + timedelta(s=1)) < now]
        for c in exp_codes:
            rsn = "used" if one_time_codes.get(c, {}).get('used') else "expired_code"
            one_time_codes.pop(c, None); app.logger.info(f"Periodic cleanup: Removed OTC {c} ({rsn}).")
        app.logger.debug("Periodic cleanup finished.")

if os.environ.get("WERKZEUG_RUN_MAIN") == "true" or not app.debug:
    threading.Thread(target=periodic_cleanup_task, daemon=True).start()

def get_admin_dashboard_state(admin_s_data):
    if not admin_s_data or admin_s_data.get('role') != 'admin': return {}

    current_wheel = admin_s_data.get('wheel_type', 'american')
    tracker = admin_s_data.get("tracker_instance")

    if not tracker or tracker.wheel_type != current_wheel:
        app.logger.warning(f"Tracker missing or wheel type mismatch for {admin_s_data.get('username')}. Re-initializing with {current_wheel} wheel.")
        tracker = RouletteTracker(wheel_type=current_wheel)
        admin_s_data["tracker_instance"] = tracker

    spins_hist = tracker.history # Internal name, refers to list of numbers
    entry_ct = tracker.entry_count()
    status_ind = get_status_indicator(entry_ct)
    tracker_unhit_cat, _, _ = tracker.get_unhit_numbers()
    hitting_set_nums = compute_best_greedy_hitting_set(spins_hist) if len(spins_hist) >= 3 else set()
    hitting_set_cat = categorize_numbers_for_display(hitting_set_nums)
    curr_distill_cat, curr_distill_ct = calculate_distilled_output(tracker_unhit_cat, hitting_set_cat)
    prev_distill_disp = admin_s_data.get("previous_distill_output_state", {"categories": None, "count": 0})
    avg_spins_w = (admin_s_data.get("sum_of_spins_between_wins",0) / admin_s_data.get("total_wins",1)) if admin_s_data.get("total_wins",0)>0 else 0.0

    return {
        "status": status_ind, "entryCount": entry_ct,
        "saveTarget": admin_s_data.get("current_save_file_display_name", f"{DEFAULT_SAVE_FILE_PREFIX}_{admin_s_data.get('username','admin')}.txt"),
        "lastSpin": tracker.last_spin, # Internal: last number entered
        "spinHistoryCount": len(spins_hist), # Internal: count of numbers in history
        "spinHistoryList": list(spins_hist), # Internal: list of numbers
        "previousDistillOutput": prev_distill_disp,
        "distillOutput": {"categories": curr_distill_cat, "count": curr_distill_ct},
        "spinsSinceLastMatch": admin_s_data.get("spins_since_last_match", 0),
        "maxWinStreak": admin_s_data.get("max_win_streak", 0),
        "maxLossStreak": admin_s_data.get("max_loss_streak", 0),
        "avgSpinsBetweenWins": f"{avg_spins_w:.1f}",
        "connectedUserCount": len(admin_s_data.get("user_connections", set())),
        "adminUsername": admin_s_data.get("username", "Admin"),
        "currentWheelType": current_wheel,
        "ttsData": curr_distill_cat
    }

# --- Routes ---
@app.route('/')
def route_index(): return render_template('index.html')

@app.route('/admin-login', methods=['GET', 'POST'])
def route_admin_login():
    if request.method == 'POST':
        uname, passwd = request.form.get('username'), request.form.get('password')
        ip, req_dets = request.remote_addr, get_request_details(request)
        if not check_rate_limit(ip, "login", LOGIN_ATTEMPT_LIMIT, LOGIN_ATTEMPT_WINDOW):
            log_event("sec.failed_login", uname or "N/A", ip, "fail", req_dets, details={"reason": "Rate limit"})
            return jsonify({"success": False, "message": "Too many login attempts."}), 429
        user_d = admin_users.get(uname)
        if user_d and check_password_hash(user_d['hashed_password'], passwd):
            admin_s_id = secrets.token_hex(16)
            default_wheel = 'american'
            active_sessions[admin_s_id] = {
                "username": uname, "role": "admin", "last_activity": datetime.now(timezone.utc),
                "wheel_type": default_wheel,
                "tracker_instance": RouletteTracker(wheel_type=default_wheel),
                "user_connections": set(), "user_sids_map": {},
                "current_save_file_display_name": f"numbers_session_{uname}.txt", # Changed default prefix
                "spins_since_last_match": 0, "current_win_streak": 0, "max_win_streak": 0,
                "max_loss_streak": 0, "total_wins": 0, "sum_of_spins_between_wins": 0,
                "previous_distill_output_state": {"categories": None, "count": 0},
                "prev_best_set_for_match_logic": set()
            }
            session['session_id'], session['username'], session['role'] = admin_s_id, uname, 'admin'
            log_event("sec.login", uname, ip, "ok", req_dets, details={"s_id": admin_s_id})
            return jsonify({"success": True, "session_id": admin_s_id, "username": uname, "role": "admin"})
        else:
            log_event("sec.failed_login", uname or "N/A", ip, "fail", req_dets, details={"reason": "Invalid creds"})
            return jsonify({"success": False, "message": "Invalid username or password."}), 401
    if 'session_id' in session and get_session_if_valid(session['session_id']):
        return redirect(url_for('route_admin_dashboard'))
    return render_template('admin_login.html')

@app.route('/admin-dashboard')
def route_admin_dashboard():
    flask_s_id = session.get('session_id')
    admin_s_d = get_session_if_valid(flask_s_id)
    if not admin_s_d or admin_s_d.get('role')!='admin' or admin_s_d.get('username')!=session.get('username'):
        session.clear(); return redirect(url_for('route_admin_login'))
    return render_template('admin_dashboard.html', session_id=flask_s_id, username=admin_s_d['username'])

@app.route('/user-portal')
def route_user_portal():
    code, admin_s_id_user = request.args.get('code'), request.args.get('admin_session_id')
    if not code or not admin_s_id_user: return redirect(url_for('route_index'))
    return render_template('user_portal.html', one_time_code=code, admin_session_id=admin_s_id_user)

@app.route('/api/logout', methods=['POST'])
def api_logout():
    flask_s_id = session.pop('session_id', None)
    uname = session.pop('username', 'unknown_logout')
    session.pop('role', None)
    if flask_s_id:
        # Get the session data before cleaning up
        admin_s_d = active_sessions.get(flask_s_id)
        if admin_s_d and admin_s_d.get('role') == 'admin':
            # Notify all connected users that the admin is terminating the session
            user_connections = list(admin_s_d.get("user_connections", set()))
            for user_sid in user_connections:
                socketio.emit('admin_terminated_session', {
                    'message': 'Admin has ended the session. You will be disconnected.'
                }, room=user_sid)

        # Save session and clean up
        cleanup_session(flask_s_id, reason="logout")
        log_event("sec.logout", uname, request.remote_addr, "ok", get_request_details(request), details={"s_id": flask_s_id})
    return jsonify({"success": True, "message": "Logged out."})

@app.route('/api/generate-code', methods=['POST'])
def api_generate_code():
    flask_s_id = session.get('session_id')
    admin_s_d = get_session_if_valid(flask_s_id)
    ip, req_dets = request.remote_addr, get_request_details(request)
    if not admin_s_d or admin_s_d.get('role') != 'admin': return jsonify({"success": False, "message": "Unauthorized."}), 401
    if not check_rate_limit(ip, "code_gen", CODE_GEN_LIMIT, CODE_GEN_WINDOW):
        log_event("admin.gen_code", admin_s_d['username'], ip, "fail", req_dets, details={"reason": "Rate limit"})
        return jsonify({"success": False, "message": "Rate limit exceeded."}), 429
    new_c = generate_one_time_code_value()
    while new_c in one_time_codes: new_c = generate_one_time_code_value()
    one_time_codes[new_c] = {
        "session_id": flask_s_id, "expires_at": datetime.now(timezone.utc) + ONE_TIME_CODE_VALIDITY,
        "admin_username": admin_s_d['username'], "used": False
    }
    log_event("admin.gen_code", admin_s_d['username'], ip, "ok", req_dets, details={"code": new_c, "s_id": flask_s_id})
    return jsonify({"success": True, "code": new_c, "expires_in_seconds": ONE_TIME_CODE_VALIDITY.total_seconds()})

@app.route('/api/validate-code', methods=['POST'])
def api_validate_code():
    data = request.get_json(); code_in = data.get('code', "").strip()
    ip, req_dets = request.remote_addr, get_request_details(request)
    if not code_in: return jsonify({"success": False, "message": "Code required."}), 400
    if code_in.lower() == "admin":
        log_event("user.val_code", "N/A", ip, "info", req_dets, details={"code": code_in, "action": "redir_admin_login"})
        return jsonify({"success": True, "action": "redirect_admin_login"})
    code_d_stored = one_time_codes.get(code_in)
    if not code_d_stored:
        log_event("user.val_code", "N/A", ip, "fail", req_dets, details={"code": code_in, "reason": "Not found"})
        return jsonify({"success": False, "message": "Invalid or expired code."}), 404
    if code_d_stored['used']:
        log_event("user.val_code", "N/A", ip, "fail", req_dets, details={"code": code_in, "reason": "Used"})
        return jsonify({"success": False, "message": "Code already used."}), 403
    if datetime.now(timezone.utc) > code_d_stored['expires_at']:
        log_event("user.val_code", "N/A", ip, "fail", req_dets, details={"code": code_in, "reason": "Expired"})
        one_time_codes.pop(code_in, None); return jsonify({"success": False, "message": "Code expired."}), 404
    target_admin_s_id = code_d_stored['session_id']
    admin_session_active = get_session_if_valid(target_admin_s_id) # check if admin session is still valid
    if not admin_session_active:
        log_event("user.val_code", "N/A", ip, "fail", req_dets, details={"code": code_in, "reason": "Admin session inactive"})
        one_time_codes.pop(code_in, None); return jsonify({"success": False, "message": "Admin session inactive."}), 404

    # Pass admin username to user portal for display (though user portal will simplify display)
    admin_username_for_user = admin_session_active.get('username', 'Admin')
    log_event("user.val_code", "N/A", ip, "ok", req_dets, details={"code": code_in, "admin_s_id": target_admin_s_id})
    return jsonify({
        "success": True,
        "message": "Code valid.",
        "admin_session_id": target_admin_s_id,
        "admin_username": admin_username_for_user, # Send admin username
        "action": "proceed_to_user_portal"
    })

# --- Spin History Management API Routes ---
@app.route('/api/admin/history/list', methods=['GET'])
def api_admin_list_history():
    flask_s_id = session.get('session_id')
    admin_s_d = get_session_if_valid(flask_s_id)
    if not admin_s_d or admin_s_d.get('role') != 'admin':
        return jsonify({"success": False, "message": "Unauthorized."}), 401

    meta = _load_session_metadata()
    return jsonify({"success": True, "histories": meta.get('sessions', [])})

@app.route('/api/admin/history/view/<path:filename>', methods=['GET'])
def api_admin_view_history(filename):
    flask_s_id = session.get('session_id')
    admin_s_d = get_session_if_valid(flask_s_id)
    if not admin_s_d or admin_s_d.get('role') != 'admin':
        return jsonify({"success": False, "message": "Unauthorized."}), 401

    safe_filename = os.path.basename(filename)
    # Regex updated to match new timestamp format (includes microseconds)
    if not re.match(r'^session_[a-zA-Z0-9_-]+\d{8}_\d{6}_\d{6}_[a-zA-Z0-9_-]+\.txt$', safe_filename):
        app.logger.warning(f"Invalid filename format attempt: {safe_filename}")
        return jsonify({"success": False, "message": "Invalid filename format."}), 400

    file_path = os.path.join(SESSION_HISTORY_DIRECTORY, safe_filename)
    if not os.path.exists(file_path) or not os.path.isfile(file_path):
        return jsonify({"success": False, "message": "History file not found."}), 404

    if os.path.commonprefix((os.path.realpath(file_path), os.path.realpath(SESSION_HISTORY_DIRECTORY))) != os.path.realpath(SESSION_HISTORY_DIRECTORY):
        return jsonify({"success": False, "message": "Access denied to file path."}), 403

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        return jsonify({"success": True, "filename": safe_filename, "content": content})
    except Exception as e:
        app.logger.error(f"Error reading history file {safe_filename}: {e}")
        return jsonify({"success": False, "message": "Error reading file."}), 500

@app.route('/api/admin/history/delete/<path:filename>', methods=['DELETE'])
def api_admin_delete_history(filename):
    flask_s_id = session.get('session_id')
    admin_s_d = get_session_if_valid(flask_s_id)
    if not admin_s_d or admin_s_d.get('role') != 'admin':
        return jsonify({"success": False, "message": "Unauthorized."}), 401

    safe_filename = os.path.basename(filename)
    if not re.match(r'^session_[a-zA-Z0-9_-]+\d{8}_\d{6}_\d{6}_[a-zA-Z0-9_-]+\.txt$', safe_filename):
        app.logger.warning(f"Invalid filename format for delete: {safe_filename}")
        return jsonify({"success": False, "message": "Invalid filename format."}), 400

    file_path = os.path.join(SESSION_HISTORY_DIRECTORY, safe_filename)

    deleted_file = False
    if os.path.exists(file_path) and os.path.isfile(file_path):
        if os.path.commonprefix((os.path.realpath(file_path), os.path.realpath(SESSION_HISTORY_DIRECTORY))) != os.path.realpath(SESSION_HISTORY_DIRECTORY):
            return jsonify({"success": False, "message": "Access denied to file path for deletion."}), 403
        try:
            os.remove(file_path)
            deleted_file = True
        except Exception as e:
            app.logger.error(f"Error deleting history file {safe_filename}: {e}")
            return jsonify({"success": False, "message": f"Error deleting file: {str(e)}"}), 500

    meta = _load_session_metadata()
    original_len = len(meta.get('sessions', []))
    meta['sessions'] = [s for s in meta.get('sessions', []) if s.get('filename') != safe_filename]

    if len(meta.get('sessions', [])) < original_len or deleted_file:
        _save_session_metadata(meta)
        log_event("admin.delete_history", admin_s_d['username'], request.remote_addr, "success", details={"filename": safe_filename})
        return jsonify({"success": True, "message": f"History '{safe_filename}' deleted."})
    else:
        return jsonify({"success": False, "message": "History file not found in records or on disk."}), 404


# --- SocketIO Event Handlers ---
@socketio.on('connect')
def sio_connect():
    app.logger.info(f"Client connected: SID {request.sid}, IP {request.remote_addr}")
    emit('connection_ack', {'sid': request.sid, 'message': 'Connected to server.'})


@socketio.on('disconnect')
def sio_disconnect():
    app.logger.info(f"Client disconnected: SID {request.sid}")
    for admin_s_id, admin_s_d_iter in list(active_sessions.items()):
        if request.sid in admin_s_d_iter.get("user_connections", set()):
            admin_s_d_iter["user_connections"].remove(request.sid)
            temp_uid = admin_s_d_iter.get("user_sids_map", {}).pop(request.sid, f"sid_{request.sid[:6]}")
            log_event("user.dc_session", temp_uid, request.remote_addr, "ok", details={"s_id": admin_s_id, "sid": request.sid})
            upd_admin_state = get_admin_dashboard_state(admin_s_d_iter)
            socketio.emit('session_update', upd_admin_state, room=admin_s_id)
            socketio.emit('user_left_notification', {'temp_user_id': temp_uid, 'user_count': len(admin_s_d_iter["user_connections"])}, room=admin_s_id)
            break

@socketio.on('join_admin_session')
def sio_join_admin_session(data):
    admin_s_id_client = data.get('session_id')
    flask_s_id = session.get('session_id')
    if not flask_s_id or flask_s_id != admin_s_id_client:
        emit('error_critical', {'message': 'Session mismatch. Re-login.'}); app.logger.warning(f"Admin join mismatch. Flask: {flask_s_id}, Client: {admin_s_id_client}"); return
    admin_s_d = get_session_if_valid(admin_s_id_client)
    ip = request.remote_addr
    if not admin_s_d or admin_s_d.get('username') != session.get('username'):
        emit('error_critical', {'message': 'Admin session invalid. Re-login.'}); log_event("admin.join_socket", session.get('username','unknown'), ip, "fail", details={"reason":"Invalid session join", "s_id":admin_s_id_client}); return
    join_room(admin_s_id_client)
    log_event("admin.join_socket", admin_s_d['username'], ip, "ok", details={"s_id": admin_s_id_client, "sid": request.sid})
    app.logger.info(f"Admin {admin_s_d['username']} (SID: {request.sid}) joined room {admin_s_id_client}")
    emit('session_update', get_admin_dashboard_state(admin_s_d))

@socketio.on('join_user_to_admin_session')
def sio_join_user(data):
    otc_user, target_admin_s_id = data.get('code'), data.get('admin_session_id')
    ip = request.remote_addr
    if not otc_user or not target_admin_s_id:
        emit('user_join_failed', {'message': 'Missing information for joining.'}); return

    code_dets = one_time_codes.get(otc_user)
    if not code_dets or code_dets.get('used') or datetime.now(timezone.utc) > code_dets.get('expires_at') or code_dets.get('session_id') != target_admin_s_id:
        emit('user_join_failed', {'message': 'Invalid, used, or expired code for this session.'});
        if code_dets and (code_dets.get('used') or datetime.now(timezone.utc) > code_dets['expires_at']):
            one_time_codes.pop(otc_user, None) # Clean up used/expired code
        log_event("user.join_socket", "N/A", ip, "fail", details={"code":otc_user, "reason":"Invalid code socket join"}); return

    admin_s_d_user = get_session_if_valid(target_admin_s_id)
    if not admin_s_d_user:
        emit('user_join_failed', {'message': 'Admin session is currently inactive.'});
        one_time_codes.pop(otc_user, None) # Clean up code if admin session is gone
        log_event("user.join_socket", "N/A", ip, "fail", details={"code":otc_user, "reason":"Admin session not found socket join"}); return

    join_room(target_admin_s_id); one_time_codes[otc_user]['used'] = True
    temp_uid = f"User_{secrets.token_hex(3)}" # This ID is not really used by user portal anymore.
    admin_s_d_user.setdefault("user_connections", set()).add(request.sid)
    admin_s_d_user.setdefault("user_sids_map", {})[request.sid] = temp_uid

    log_event("user.join_s_socket", temp_uid, ip, "ok", details={"code":otc_user, "admin_s_id":target_admin_s_id, "admin_user":code_dets['admin_username']})
    app.logger.info(f"User (SID: {request.sid}, TempID: {temp_uid}) joined admin {code_dets['admin_username']}'s session {target_admin_s_id}")

    # Send success specifically to the joining user
    emit('user_join_success', {
        'message': f'Successfully connected to session by {code_dets["admin_username"]}.',
        'admin_username': code_dets["admin_username"] # Pass admin username for potential use
    })

    # Update admin about the new user and total count
    upd_admin_state = get_admin_dashboard_state(admin_s_d_user)
    socketio.emit('session_update', upd_admin_state, room=target_admin_s_id) # Update all in admin room
    socketio.emit('user_joined_notification', {'temp_user_id': temp_uid, 'user_count': len(admin_s_d_user["user_connections"])}, room=target_admin_s_id)


@socketio.on('admin_action')
def sio_admin_action(data):
    action, admin_s_id = data.get('action_type'), session.get('session_id')
    admin_s_d = get_session_if_valid(admin_s_id)
    if not admin_s_d or admin_s_d.get('role') != 'admin': emit('error_critical', {'message': 'Unauthorized.'}); return

    current_wheel_type = admin_s_d.get('wheel_type', 'american')
    tracker = admin_s_d.get("tracker_instance")
    if not tracker or tracker.wheel_type != current_wheel_type:
        tracker = RouletteTracker(wheel_type=current_wheel_type)
        admin_s_d["tracker_instance"] = tracker
        app.logger.warning(f"Tracker re-initialized due to wheel type mismatch in admin_action for {admin_s_d['username']}")

    log_user, ip, success, err_msg = admin_s_d['username'], request.remote_addr, False, None

    curr_hit_set = compute_best_greedy_hitting_set(tracker.history) if len(tracker.history) >=3 else set()
    curr_hit_cat = categorize_numbers_for_display(curr_hit_set)
    curr_tracker_unhit, _, _ = tracker.get_unhit_numbers()
    dist_before_cat, dist_before_ct = calculate_distilled_output(curr_tracker_unhit, curr_hit_cat)
    admin_s_d["previous_distill_output_state"] = {"categories": dist_before_cat, "count": dist_before_ct}

    if action == 'submit_spin': # Admin submits a number
        spin_val = str(data.get('spin_value', '')).strip() # 'spin_value' is param name
        try:
            if tracker.update_spin(spin_val): # update_spin is internal method name
                log_event("admin.submit_number", log_user, ip, "ok", details={"number":spin_val, "s_id":admin_s_id, "wheel": current_wheel_type}); success = True
                admin_s_d["spins_since_last_match"] = admin_s_d.get("spins_since_last_match", 0) + 1
        except ValueError as e: err_msg = str(e); log_event("admin.submit_number", log_user, ip, "fail", details={"number":spin_val, "err":str(e), "s_id":admin_s_id, "wheel": current_wheel_type})
    elif action == 'undo_spin': # Admin undoes last number
        if tracker.undo_last_spin(): log_event("admin.undo_number", log_user, ip, "ok", details={"s_id":admin_s_id}); success = True
        else: err_msg = "No entry to undo."; log_event("admin.undo_number", log_user, ip, "fail", details={"reason":err_msg, "s_id":admin_s_id})
    elif action == 'save_session': # Admin saves the current session
        if save_admin_session_history_file(log_user, tracker.history, admin_s_id, current_wheel_type):
            log_event("admin.save_session", log_user, ip, "ok", details={"s_id":admin_s_id}); success = True
            emit('action_feedback', {'message': 'Session saved successfully.', 'type': 'success'})
        else:
            err_msg = "Failed to save session."; log_event("admin.save_session", log_user, ip, "fail", details={"reason":err_msg, "s_id":admin_s_id})
    elif action == 'reset_session':
        save_admin_session_history_file(log_user, tracker.history, admin_s_id, current_wheel_type)
        tracker.reset_tracker_and_history()
        admin_s_d.update({k:0 for k in ["spins_since_last_match","current_win_streak"]})
        admin_s_d["previous_distill_output_state"] = {"categories":None,"count":0}; admin_s_d["prev_best_set_for_match_logic"]=set()
        log_event("admin.reset_session", log_user, ip, "ok", details={"s_id":admin_s_id}); success = True
        emit('action_feedback', {'message': 'Session reset, history saved.', 'type': 'success_major'})
    elif action == 'change_file': # Admin changes display name for saving context
        new_base = data.get('filename_base','').strip()
        if new_base and len(new_base) > 3:
            admin_s_d["current_save_file_display_name"] = f"{new_base}.txt" # This is just a display name
            log_event("admin.change_file_context", log_user, ip, "ok", details={"new_base":new_base, "s_id":admin_s_id}); success = True
            save_admin_session_history_file(log_user, tracker.history, admin_s_id, current_wheel_type)
            tracker.reset_tracker_and_history()
            admin_s_d.update({"spins_since_last_match":0, "previous_distill_output_state":{"categories":None,"count":0}})
            emit('action_feedback', {'message': f'Save context display updated to {admin_s_d["current_save_file_display_name"]} & session reset.', 'type': 'success_major'})
        else: err_msg = "Invalid filename base."

    if err_msg: emit('error_toast', {'message': err_msg})
    socketio.emit('session_update', get_admin_dashboard_state(admin_s_d), room=admin_s_id)

@socketio.on('admin_change_wheel_type')
def sio_admin_change_wheel_type(data):
    admin_s_id = session.get('session_id')
    admin_s_d = get_session_if_valid(admin_s_id)
    if not admin_s_d or admin_s_d.get('role') != 'admin':
        emit('error_critical', {'message': 'Unauthorized for wheel change.'})
        return

    new_wheel_type = data.get('wheel_type')
    if new_wheel_type not in ['american', 'european']:
        emit('error_toast', {'message': 'Invalid wheel type specified.'})
        return

    log_user, ip = admin_s_d['username'], request.remote_addr
    old_wheel_type = admin_s_d.get('wheel_type', 'unknown')

    tracker_old = admin_s_d.get("tracker_instance")
    if tracker_old and tracker_old.history:
        save_admin_session_history_file(log_user, tracker_old.history, admin_s_id, old_wheel_type)

    admin_s_d['wheel_type'] = new_wheel_type
    admin_s_d['tracker_instance'] = RouletteTracker(wheel_type=new_wheel_type)

    admin_s_d.update({
        "spins_since_last_match": 0,
        "current_win_streak": 0,
        "previous_distill_output_state": {"categories": None, "count": 0},
        "prev_best_set_for_match_logic": set()
    })

    log_event("admin.change_wheel", log_user, ip, "ok", details={
        "s_id": admin_s_id,
        "old_wheel_type": old_wheel_type,
        "new_wheel_type": new_wheel_type
    })
    emit('action_feedback', {'message': f'Wheel type changed to {new_wheel_type.capitalize()}. Session and history reset.', 'type': 'success_major'})
    socketio.emit('session_update', get_admin_dashboard_state(admin_s_d), room=admin_s_id)


@socketio.on('user_submit_spin') # Event name kept for simplicity, but it's submitting numbers
def sio_user_submit_numbers(data): # Renamed handler for clarity
    target_admin_s_id, number_val_str = data.get('admin_session_id'), str(data.get('spin_value','')).strip() # 'spin_value' is param name
    admin_s_d = get_session_if_valid(target_admin_s_id)
    if not admin_s_d: emit('error_toast', {'message': 'Admin session inactive.'}); return
    if request.sid not in admin_s_d.get("user_connections",set()): emit('error_critical',{'message':'Not authorized.'}); return

    current_wheel_type = admin_s_d.get('wheel_type', 'american')
    tracker = admin_s_d.get("tracker_instance")
    if not tracker or tracker.wheel_type != current_wheel_type:
        emit('error_toast',{'message':'Admin tracker unavailable or wheel mismatch.'}); return

    temp_uid = admin_s_d.get("user_sids_map",{}).get(request.sid, f"UserSID_{request.sid[:6]}"); ip = request.remote_addr

    curr_hit_s = compute_best_greedy_hitting_set(tracker.history) if len(tracker.history)>=3 else set()
    curr_hit_c = categorize_numbers_for_display(curr_hit_s)
    curr_tracker_unhit,_,_ = tracker.get_unhit_numbers()
    dist_bf_cat,dist_bf_ct = calculate_distilled_output(curr_tracker_unhit, curr_hit_c)
    admin_s_d["previous_distill_output_state"] = {"categories":dist_bf_cat, "count":dist_bf_ct}
    try:
        if tracker.update_spin(number_val_str): # update_spin is internal tracker method
            log_event("user.submit_number", temp_uid, ip, "ok", details={"number":number_val_str, "admin_s_id":target_admin_s_id, "wheel": current_wheel_type})
            emit('action_feedback', {'message': f'Numbers "{number_val_str}" submitted.', 'type': 'success'}) # Updated message
            admin_s_d["spins_since_last_match"] = admin_s_d.get("spins_since_last_match", 0) + 1
            socketio.emit('session_update', get_admin_dashboard_state(admin_s_d), room=target_admin_s_id)
    except ValueError as e:
        log_event("user.submit_number", temp_uid, ip, "fail", details={"number":number_val_str, "err":str(e), "admin_s_id":target_admin_s_id, "wheel": current_wheel_type})
        emit('error_toast', {'message': str(e)})

@socketio.on('user_undo_last_spin') # Event name kept
def sio_user_undo_last_entry(data): # Renamed handler for clarity
    target_admin_s_id = data.get('admin_session_id')
    admin_s_d = get_session_if_valid(target_admin_s_id)
    if not admin_s_d: emit('error_toast', {'message': 'Admin session inactive.'}); return
    if request.sid not in admin_s_d.get("user_connections",set()): emit('error_critical',{'message':'Not authorized.'}); return

    current_wheel_type = admin_s_d.get('wheel_type', 'american')
    tracker = admin_s_d.get("tracker_instance")
    if not tracker or tracker.wheel_type != current_wheel_type:
        emit('error_toast',{'message':'Admin tracker unavailable or wheel mismatch.'}); return

    temp_uid = admin_s_d.get("user_sids_map",{}).get(request.sid, f"UserSID_{request.sid[:6]}"); ip = request.remote_addr

    curr_hit_s = compute_best_greedy_hitting_set(tracker.history) if len(tracker.history)>=3 else set()
    curr_hit_c = categorize_numbers_for_display(curr_hit_s)
    curr_tracker_unhit,_,_ = tracker.get_unhit_numbers()
    dist_bf_cat,dist_bf_ct = calculate_distilled_output(curr_tracker_unhit, curr_hit_c)
    admin_s_d["previous_distill_output_state"] = {"categories":dist_bf_cat, "count":dist_bf_ct}

    if tracker.undo_last_spin(): # undo_last_spin is internal tracker method
        log_event("user.undo_entry", temp_uid, ip, "ok", details={"admin_s_id":target_admin_s_id})
        emit('action_feedback', {'message': 'Last entry undone.', 'type': 'success'}) # Updated message
        socketio.emit('session_update', get_admin_dashboard_state(admin_s_d), room=target_admin_s_id)
    else:
        log_event("user.undo_entry", temp_uid, ip, "fail", details={"reason":"No entry to undo", "admin_s_id":target_admin_s_id})
        emit('error_toast', {'message': 'No entry to undo.'})

if __name__ == '__main__':
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(name)s - %(module)s:%(lineno)d - %(message)s')
    app.logger.info("Starting Flask-SocketIO server...")
    socketio.run(app, host='0.0.0.0', port=8081, debug=True, use_reloader=True, allow_unsafe_werkzeug=True)

// --- File: static/js/main.js ---
document.addEventListener('DOMContentLoaded', () => {
    const pagePath = window.location.pathname;
    let socket;
    let adminUsernameForUserPortal = ''; // Store admin username for user portal

    function displayMessage(text, type = 'error', areaId) {
        const area = document.getElementById(areaId);
        if (area) {
            area.textContent = text;
            area.className = 'message-box';
            if (type) area.classList.add(type === 'error' ? 'error-box' : (type === 'success' ? 'success-box' : 'info-box'));
            area.style.display = 'block';
            if (type === 'success' || type === 'info' || type === 'success_major') {
                setTimeout(() => {
                    if (area.textContent === text) {
                        clearMessage(areaId);
                    }
                }, 7000); // Increased timeout slightly
            }
        } else {
            console.warn(`Message area with ID '${areaId}' not found for message: ${text}`);
        }
    }
    function clearMessage(areaId) {
        const area = document.getElementById(areaId);
        if (area) {
            area.textContent = '';
            area.style.display = 'none';
        }
    }

    if (pagePath === '/') {
        const userJoinForm = document.getElementById('user-join-form');
        const codeInput = document.getElementById('one-time-code');
        const messageAreaId = 'user-join-message-area';

        if (userJoinForm && codeInput) {
            userJoinForm.addEventListener('submit', async (e) => {
                e.preventDefault();
                clearMessage(messageAreaId);
                const code = codeInput.value.trim();
                if (!code) {
                    displayMessage('Please enter a code.', 'error', messageAreaId);
                    return;
                }
                if (code.toLowerCase() === 'admin') {
                    displayMessage('Proceeding to admin login...', 'info', messageAreaId);
                    window.location.href = '/admin-login';
                    return;
                }
                try {
                    const response = await fetch('/api/validate-code', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ code: code })
                    });
                    const data = await response.json();
                    if (data.success) {
                        if (data.action === 'redirect_admin_login') {
                            displayMessage('Proceeding to admin login...', 'info', messageAreaId);
                            window.location.href = '/admin-login';
                        } else if (data.action === 'proceed_to_user_portal') {
                            displayMessage('Code valid. Redirecting to user portal...', 'success', messageAreaId);
                            // Store admin username from validation for user portal page
                            if(data.admin_username) localStorage.setItem('admin_username_for_user', data.admin_username);
                            window.location.href = `/user-portal?code=${encodeURIComponent(code)}&admin_session_id=${encodeURIComponent(data.admin_session_id)}`;
                        } else {
                             displayMessage(data.message || 'Unexpected response from server.', 'error', messageAreaId);
                        }
                    } else {
                        displayMessage(data.message || 'Invalid code or error validating code.', 'error', messageAreaId);
                    }
                } catch (error) {
                    console.error("Error validating code:", error);
                    displayMessage('Network error or server issue. Please try again.', 'error', messageAreaId);
                }
            });
        }
    }

    if (pagePath.includes('/admin-login')) {
        const loginForm = document.getElementById('admin-login-form');
        const messageAreaId = 'login-message-area';
        if (loginForm) {
            loginForm.addEventListener('submit', async (e) => {
                e.preventDefault();
                clearMessage(messageAreaId);
                const formData = new FormData(loginForm);
                try {
                    const response = await fetch('/admin-login', {
                        method: 'POST',
                        body: formData
                    });
                    const data = await response.json();
                    if (data.success) {
                        localStorage.setItem('admin_session_id', data.session_id);
                        localStorage.setItem('admin_username', data.username);
                        window.location.href = '/admin-dashboard';
                    } else {
                        displayMessage(data.message || 'Login failed.', 'error', messageAreaId);
                    }
                } catch (error) {
                    console.error("Login error:", error);
                    displayMessage('Network error or server issue during login.', 'error', messageAreaId);
                }
            });
        }
    }

    if (pagePath.includes('/admin-dashboard')) {
        const adminSessionIdFromStorage = localStorage.getItem('admin_session_id');
        if (!adminSessionIdFromStorage && (typeof ADMIN_SESSION_ID === 'undefined' || !ADMIN_SESSION_ID) ) {
            window.location.href = '/admin-login';
            return;
        }
        const currentAdminSessionId = adminSessionIdFromStorage || ADMIN_SESSION_ID;
        socket = io({ transports: ['polling'] });

        const spinInput = document.getElementById('spin-input'); // ID kept as spin-input for minimal JS change, label changed in HTML
        const submitButton = document.getElementById('submit-spin-button'); // ID kept
        const undoButton = document.getElementById('undo-button');
        const resetButton = document.getElementById('reset-button');
        const changeFileButton = document.getElementById('change-file-button');

        const ttsStopButton = document.getElementById('tts-stop-button');
        const ttsMuteButton = document.getElementById('tts-mute-button');
        const ttsRepeatButton = document.getElementById('tts-repeat-button');
        const ttsSlowerButton = document.getElementById('tts-slower-button');
        const ttsFasterButton = document.getElementById('tts-faster-button');
        const ttsRateDisplay = document.getElementById('tts-rate-display');
        const ttsRepetitionsSelect = document.getElementById('tts-repetitions-select');

        const statusIndicatorEl = document.getElementById('status-indicator');
        const entryCountEl = document.getElementById('entry-count');
        const saveTargetEl = document.getElementById('save-target');
        const lastSpinDisplayEl = document.getElementById('last-spin-display'); // Displays last number/entry
        const lastMatchDisplayEl = document.getElementById('last-match-display');
        const maxWinStreakDisplayEl = document.getElementById('max-win-streak');
        const maxLossStreakDisplayEl = document.getElementById('max-loss-streak');
        const avgSpinsWinDisplayEl = document.getElementById('avg-spins-win'); // Displays avg entries/win
        const spinHistoryListEl = document.getElementById('spin-history-list'); // Displays list of numbers/entries
        const spinHistoryCountDisplayEl = document.getElementById('spin-history-count-display'); // Count of numbers/entries
        const currentWheelTypeDisplayEl = document.getElementById('current-wheel-type-display');
        const wheelTypeSelectEl = document.getElementById('wheel-type-select');
        const changeWheelTypeButtonEl = document.getElementById('change-wheel-type-button');

        const distillOutputEl = document.getElementById('distill-output');
        const previousDistillOutputEl = document.getElementById('previous-distill-output');
        const adminMessageAreaId = 'admin-message-area';

        const refreshHistoryListButton = document.getElementById('refresh-history-list-button');
        const savedHistorySelect = document.getElementById('saved-history-select');
        const viewHistoryButton = document.getElementById('view-history-button');
        const deleteHistoryButton = document.getElementById('delete-history-button');
        const historyViewAreaContainer = document.getElementById('history-view-area-container');
        const viewingHistoryFilenameEl = document.getElementById('viewing-history-filename');
        const historyViewArea = document.getElementById('history-view-area');
        const historyMgmtMsgAreaId = 'history-management-message-area';

        const SPEECH_RATE_STEP = 0.1, MIN_SPEECH_RATE = 0.5, MAX_SPEECH_RATE = 2.0;
        let currentSpeechRate = 1.0, isSpeechMuted = false, speechRepetitions = 1;

        function updateTtsRateDisplay() { if (ttsRateDisplay) ttsRateDisplay.textContent = `Rate: ${currentSpeechRate.toFixed(1)}`; }
        updateTtsRateDisplay();
        if(historyViewAreaContainer) historyViewAreaContainer.style.display = 'none';

        function formatCategoriesForPreDisplay(categoriesObj, title = '') {
            let lines = title ? [title] : [];
            if (!categoriesObj || typeof categoriesObj !== 'object' || Object.keys(categoriesObj).length === 0) {
                lines.push('(No data)'); return lines.join('\n');
            }
            ['Zs', 'D1', 'D2', 'D3'].forEach(key => {
                if (categoriesObj.hasOwnProperty(key)) {
                    let nums = categoriesObj[key] || [];

                    // Sort numbers numerically (special handling for '0' and '00')
                    nums = [...nums].sort((a, b) => {
                        // Special case for '0' and '00'
                        if (a === '0' || a === '00' || b === '0' || b === '00') {
                            if (a === '0') return -1;
                            if (b === '0') return 1;
                            if (a === '00') return -1;
                            if (b === '00') return 1;
                        }
                        // For regular numbers, convert to integers and compare
                        return parseInt(a, 10) - parseInt(b, 10);
                    });

                    lines.push(` ${key}: ${nums.length > 0 ? nums.join('-') : 'None'}`);
                }
            });
            if (lines.length === (title ? 1:0)) lines.push('(Empty categories)');
            return lines.join('\n');
        }

        function updateAdminDisplay(data) {
            if (!data) { console.error("updateAdminDisplay: no data received."); return; }
            const na = 'N/A';

            if (statusIndicatorEl) {
                const sTxt = data.status || na; statusIndicatorEl.textContent = sTxt;
                statusIndicatorEl.className = 'status-pill';
                if (sTxt === '[RED]') statusIndicatorEl.classList.add('status-red');
                else if (sTxt === '[YELLOW]') statusIndicatorEl.classList.add('status-yellow');
                else if (sTxt === '[GREEN]') statusIndicatorEl.classList.add('status-green');
                else statusIndicatorEl.classList.add('status-neutral');
            }
            if (entryCountEl) entryCountEl.textContent = `Count: ${data.entryCount !== undefined ? data.entryCount : na}`;
            if (saveTargetEl) saveTargetEl.textContent = `Save Context: ${data.saveTarget || na}`;
            if (lastSpinDisplayEl) lastSpinDisplayEl.textContent = `Last Entry: ${data.lastSpin || na}`; // lastSpin from server is last number
            if (lastMatchDisplayEl) lastMatchDisplayEl.textContent = `Entries Since Match: ${data.spinsSinceLastMatch !== undefined ? data.spinsSinceLastMatch : na}`; // spinsSinceLastMatch from server
            if (maxWinStreakDisplayEl) maxWinStreakDisplayEl.textContent = `Max Wins: ${data.maxWinStreak !== undefined ? data.maxWinStreak : na}`;
            if (maxLossStreakDisplayEl) maxLossStreakDisplayEl.textContent = `Max Losses: ${data.maxLossStreak !== undefined ? data.maxLossStreak : na}`;
            if (avgSpinsWinDisplayEl) avgSpinsWinDisplayEl.textContent = `Avg Entries/Win: ${data.avgSpinsBetweenWins || na}`; // avgSpinsBetweenWins from server

            if (currentWheelTypeDisplayEl && data.currentWheelType) {
                currentWheelTypeDisplayEl.textContent = data.currentWheelType.charAt(0).toUpperCase() + data.currentWheelType.slice(1);
                currentWheelTypeDisplayEl.className = 'status-pill status-neutral';
            }
            if (wheelTypeSelectEl && data.currentWheelType) {
                wheelTypeSelectEl.value = data.currentWheelType;
            }

            if (spinHistoryListEl) { // spinHistoryList from server contains numbers
                spinHistoryListEl.innerHTML = '';
                if (data.spinHistoryList && data.spinHistoryList.length > 0) {
                    // Display all numbers in a single continuous list
                    const li = document.createElement('li');
                    li.textContent = data.spinHistoryList.join('-');
                    spinHistoryListEl.appendChild(li);
                } else {
                    spinHistoryListEl.innerHTML = '<li>No numbers entered yet.</li>';
                }
            }
            if(spinHistoryCountDisplayEl) spinHistoryCountDisplayEl.textContent = data.spinHistoryCount !== undefined ? data.spinHistoryCount : '0'; // spinHistoryCount from server

            const prevDistill = data.previousDistillOutput, currDistill = data.distillOutput;
            if (previousDistillOutputEl) previousDistillOutputEl.textContent = formatCategoriesForPreDisplay(prevDistill?.categories, `Previous Distill (${prevDistill?.count || 0})`);
            if (distillOutputEl) distillOutputEl.textContent = formatCategoriesForPreDisplay(currDistill?.categories, `Current Distill (${currDistill?.count || 0})`);

            const connectedUsersEl = document.getElementById('connected-users-count');
            if (connectedUsersEl) connectedUsersEl.textContent = data.connectedUserCount !== undefined ? data.connectedUserCount : '0';

            if (data.ttsData && Object.keys(data.ttsData).length > 0) speakDistill(data.ttsData);
        }

        // Format numbers for proper speech pronunciation
        function formatNumberForSpeech(numStr) {
            // Special case for "00"
            if (numStr === '00') return 'zero zero';

            // For other numbers, return as is to let the speech synthesis handle them naturally
            // This will make "13" be pronounced as "thirteen" instead of "one-three"
            return numStr;
        }

        // Store the last spoken numbers for the repeat button
        let lastSpokenNumbers = [];

        function speakDistill(distillCategories) {
            if (isSpeechMuted || !('speechSynthesis' in window) || !distillCategories) return;
            const numsToSpeak = [];

            // Process categories in order: Zs, D1, D2, D3
            ['Zs', 'D1', 'D2', 'D3'].forEach(cat => {
                if (distillCategories[cat]?.length > 0) {
                    // Sort numbers within each category before adding them
                    const sortedNums = [...distillCategories[cat]].sort((a, b) => {
                        // Special case for '0' and '00'
                        if (a === '0' || a === '00' || b === '0' || b === '00') {
                            if (a === '0') return -1;
                            if (b === '0') return 1;
                            if (a === '00') return -1;
                            if (b === '00') return 1;
                        }
                        // For regular numbers, convert to integers and compare
                        return parseInt(a, 10) - parseInt(b, 10);
                    });

                    numsToSpeak.push(...sortedNums);
                }
            });

            if (numsToSpeak.length > 0) {
                // Store for repeat functionality
                lastSpokenNumbers = [...numsToSpeak];

                // Format each number individually for proper pronunciation
                const formattedNums = numsToSpeak.map(formatNumberForSpeech);

                // Join with commas and spaces for better speech pauses
                const strSpeak = formattedNums.join(', ');

                speakText(strSpeak);
            }
        }

        function speakText(text) {
            // Warm up speech synthesis to prevent volume lag on first utterance
            if (window.speechSynthesis.getVoices().length === 0) {
                window.speechSynthesis.addEventListener('voiceschanged', () => {
                    const warmupUtt = new SpeechSynthesisUtterance('');
                    warmupUtt.volume = 0.01;
                    window.speechSynthesis.speak(warmupUtt);
                }, { once: true });
            } else {
                // Immediate warmup if voices are already loaded
                const warmupUtt = new SpeechSynthesisUtterance('');
                warmupUtt.volume = 0.01;
                window.speechSynthesis.speak(warmupUtt);
            }
            
            window.speechSynthesis.cancel();
            for (let i = 0; i < speechRepetitions; i++) {
                const utt = new SpeechSynthesisUtterance(text);
                utt.rate = currentSpeechRate;
                utt.lang = 'en-US';
                window.speechSynthesis.speak(utt);
            }
        }

        function repeatLastSpoken() {
            if (lastSpokenNumbers.length > 0) {
                // lastSpokenNumbers is already sorted in speakDistill
                const formattedNums = lastSpokenNumbers.map(formatNumberForSpeech);
                const strSpeak = formattedNums.join(', ');
                speakText(strSpeak);
            }
        }

        socket.on('connect', () => {
            console.log('Admin dashboard connected via SocketIO.');
            socket.emit('join_admin_session', { session_id: currentAdminSessionId });
            fetchAndDisplayHistories();
        });
        socket.on('connect_error', (err) => { console.error('Admin dashboard SocketIO connection error:', err); displayMessage(`Socket Connection Error: ${err.message}. Try refreshing.`, 'error', adminMessageAreaId); });
        socket.on('error_critical', (d) => { displayMessage(`Critical Error: ${d.message}. You may need to re-login. Redirecting...`, 'error', adminMessageAreaId); localStorage.clear(); setTimeout(() => window.location.href = '/admin-login', 4000); });
        socket.on('error_toast', (d) => displayMessage(d.message, 'error', adminMessageAreaId));
        socket.on('action_feedback', (d) => {
            displayMessage(d.message, d.type || 'success', adminMessageAreaId);
            if (d.type === 'success_major' && historyViewAreaContainer) {
                historyViewAreaContainer.style.display = 'none';
                if(viewingHistoryFilenameEl) viewingHistoryFilenameEl.textContent = '';
                if(historyViewArea) historyViewArea.textContent = '(Select a history to view its content)';
            }
        });
        socket.on('session_update', (d) => { console.log('Admin received session_update:', d); updateAdminDisplay(d); });
        socket.on('user_joined_notification', (d) => displayMessage(`User ${d.temp_user_id} joined. Total connected: ${d.user_count}`, 'info', adminMessageAreaId));
        socket.on('user_left_notification', (d) => displayMessage(`User ${d.temp_user_id} left. Total connected: ${d.user_count}`, 'info', adminMessageAreaId));

        document.getElementById('logout-button').addEventListener('click', async () => {
            await fetch('/api/logout', { method: 'POST' }); localStorage.clear(); if(socket) socket.disconnect(); window.location.href = '/admin-login';
        });

        const genCodeBtn = document.getElementById('generate-code-button');
        const codeDispArea = document.getElementById('code-display-area');
        const copyCodeBtn = document.getElementById('copy-code-button');
        let currentGeneratedCode = '';

        if (genCodeBtn && codeDispArea) {
            genCodeBtn.addEventListener('click', async () => {
                clearMessage('code-display-area');
                if (copyCodeBtn) copyCodeBtn.style.display = 'none';
                currentGeneratedCode = '';

                try {
                    const resp = await fetch('/api/generate-code', { method: 'POST' });
                    const d = await resp.json();
                    if (d.success) {
                        currentGeneratedCode = d.code;
                        displayMessage(`${d.code}`, 'success', 'code-display-area');
                        if (copyCodeBtn) copyCodeBtn.style.display = 'inline-block';
                    } else {
                        displayMessage(d.message || 'Failed to generate code.', 'error', 'code-display-area');
                    }
                } catch (err) {
                    displayMessage('Network error generating code.', 'error', 'code-display-area');
                }
            });
        }

        if (copyCodeBtn) {
            copyCodeBtn.addEventListener('click', () => {
                if (!currentGeneratedCode) return;

                // Copy to clipboard
                navigator.clipboard.writeText(currentGeneratedCode)
                    .then(() => {
                        const originalText = copyCodeBtn.textContent;
                        copyCodeBtn.textContent = 'Copied!';
                        setTimeout(() => {
                            copyCodeBtn.textContent = originalText;
                        }, 2000);
                    })
                    .catch(err => {
                        console.error('Failed to copy code: ', err);
                        displayMessage('Failed to copy code to clipboard.', 'error', 'code-display-area');
                    });
            });
        }

        if (submitButton) submitButton.addEventListener('click', () => { // Button for submitting numbers
            const val = spinInput.value.trim(); // spinInput field is used for numbers
            if (val) { socket.emit('admin_action', { action_type: 'submit_spin', spin_value: val }); spinInput.value = ''; } // submit_spin is server event name
            else displayMessage("Please enter a number or value.", 'error', adminMessageAreaId);
        });
        if (spinInput) spinInput.addEventListener('keypress', (e) => { if (e.key === 'Enter') { e.preventDefault(); if (submitButton) submitButton.click(); }});
        if (undoButton) undoButton.addEventListener('click', () => socket.emit('admin_action', { action_type: 'undo_spin' })); // undo_spin is server event name

        // Save Session button
        const saveSessionButton = document.getElementById('save-session-button');
        if (saveSessionButton) saveSessionButton.addEventListener('click', () => {
            socket.emit('admin_action', { action_type: 'save_session' });
        });

        if (resetButton) resetButton.addEventListener('click', () => { if (confirm("Are you sure you want to reset the current session? This will save the current numbers history and clear all entries and stats for the current context.")) socket.emit('admin_action', { action_type: 'reset_session' }); });
        if (changeFileButton) changeFileButton.addEventListener('click', () => {
            let base = spinInput.value.trim(); // spinInput used for context name
            if (!base) {
                base = 'numbers'; // Default to 'numbers' if no name is provided
            }
            if (confirm(`Use '${base}' as the new save context name and reset the session? Current numbers history will be saved.`)) {
                socket.emit('admin_action', { action_type: 'change_file', filename_base: base });
                spinInput.value = '';
            }
        });

        if (changeWheelTypeButtonEl && wheelTypeSelectEl) {
            changeWheelTypeButtonEl.addEventListener('click', () => {
                const selectedWheelType = wheelTypeSelectEl.value;
                if (confirm(`Change wheel type to ${selectedWheelType.charAt(0).toUpperCase() + selectedWheelType.slice(1)} and reset session? Current numbers history will be saved.`)) {
                    socket.emit('admin_change_wheel_type', { wheel_type: selectedWheelType });
                }
            });
        }

        if (ttsStopButton) ttsStopButton.addEventListener('click', () => window.speechSynthesis.cancel());
        if (ttsMuteButton) ttsMuteButton.addEventListener('click', () => {
            isSpeechMuted = !isSpeechMuted;
            ttsMuteButton.textContent = isSpeechMuted ? 'Unmute' : 'Mute';
            ttsMuteButton.classList.toggle('active', isSpeechMuted);
            if (isSpeechMuted) window.speechSynthesis.cancel();
        });
        if (ttsRepeatButton) ttsRepeatButton.addEventListener('click', () => repeatLastSpoken());
        if (ttsSlowerButton) ttsSlowerButton.addEventListener('click', () => { currentSpeechRate = Math.max(MIN_SPEECH_RATE, currentSpeechRate - SPEECH_RATE_STEP); updateTtsRateDisplay(); });
        if (ttsFasterButton) ttsFasterButton.addEventListener('click', () => { currentSpeechRate = Math.min(MAX_SPEECH_RATE, currentSpeechRate + SPEECH_RATE_STEP); updateTtsRateDisplay(); });
        if (ttsRepetitionsSelect) ttsRepetitionsSelect.addEventListener('change', (e) => { speechRepetitions = parseInt(e.target.value, 10); });
        if ('speechSynthesis' in window && typeof window.speechSynthesis.getVoices === 'function') {
            window.speechSynthesis.getVoices();
        }

        async function fetchAndDisplayHistories() {
            clearMessage(historyMgmtMsgAreaId);
            try {
                const response = await fetch('/api/admin/history/list');
                const data = await response.json();
                if (data.success && data.histories) {
                    savedHistorySelect.innerHTML = '<option value="">-- Select a history file --</option>';
                    if (data.histories.length === 0) {
                        savedHistorySelect.innerHTML += '<option value="" disabled>No histories found</option>';
                         displayMessage("No saved histories found.", 'info', historyMgmtMsgAreaId);
                    } else {
                        data.histories.forEach(hist => {
                            const option = document.createElement('option');
                            option.value = hist.filename;
                            // Format timestamp from ISO string to locale string for display
                            let displayTimestamp = 'N/A';
                            try {
                                displayTimestamp = new Date(hist.timestamp).toLocaleString();
                            } catch (e) { console.warn("Could not parse timestamp for display:", hist.timestamp); }

                            option.textContent = `${hist.filename} (${hist.spin_count} entries, ${hist.wheel_type || 'N/A'}) - ${displayTimestamp}`;
                            savedHistorySelect.appendChild(option);
                        });
                    }
                } else {
                    displayMessage(data.message || "Failed to load histories.", 'error', historyMgmtMsgAreaId);
                }
            } catch (error) {
                console.error("Error fetching histories:", error);
                displayMessage("Network error fetching histories.", 'error', historyMgmtMsgAreaId);
            }
            if (historyViewAreaContainer) historyViewAreaContainer.style.display = 'none';
            if (viewingHistoryFilenameEl) viewingHistoryFilenameEl.textContent = '';
            if (historyViewArea) historyViewArea.textContent = '(Select a history to view its content)';
            if (viewHistoryButton) viewHistoryButton.disabled = true;
            if (deleteHistoryButton) deleteHistoryButton.disabled = true;
        }

        if (refreshHistoryListButton) {
            refreshHistoryListButton.addEventListener('click', fetchAndDisplayHistories);
        }

        if (savedHistorySelect) {
            savedHistorySelect.addEventListener('change', () => {
                const selectedFile = savedHistorySelect.value;
                if (selectedFile) {
                   if(viewHistoryButton) viewHistoryButton.disabled = false;
                   if(deleteHistoryButton) deleteHistoryButton.disabled = false;
                } else {
                   if(viewHistoryButton) viewHistoryButton.disabled = true;
                   if(deleteHistoryButton) deleteHistoryButton.disabled = true;
                   if(historyViewAreaContainer) historyViewAreaContainer.style.display = 'none';
                }
            });
        }

        if (viewHistoryButton) {
            viewHistoryButton.addEventListener('click', async () => {
                const filename = savedHistorySelect.value;
                if (!filename) {
                    displayMessage("Please select a history file to view.", 'info', historyMgmtMsgAreaId);
                    return;
                }
                clearMessage(historyMgmtMsgAreaId);
                try {
                    const response = await fetch(`/api/admin/history/view/${encodeURIComponent(filename)}`);
                    const data = await response.json();
                    if (data.success) {
                        if(historyViewArea) historyViewArea.textContent = data.content;
                        if(viewingHistoryFilenameEl) viewingHistoryFilenameEl.textContent = data.filename;
                        if(historyViewAreaContainer) historyViewAreaContainer.style.display = 'block';
                    } else {
                        displayMessage(data.message || "Failed to load history content.", 'error', historyMgmtMsgAreaId);
                        if(historyViewAreaContainer) historyViewAreaContainer.style.display = 'none';
                    }
                } catch (error) {
                    console.error("Error viewing history:", error);
                    displayMessage("Network error viewing history.", 'error', historyMgmtMsgAreaId);
                    if(historyViewAreaContainer) historyViewAreaContainer.style.display = 'none';
                }
            });
        }

        if (deleteHistoryButton) {
            deleteHistoryButton.addEventListener('click', async () => {
                const filename = savedHistorySelect.value;
                if (!filename) {
                    displayMessage("Please select a history file to delete.", 'info', historyMgmtMsgAreaId);
                    return;
                }
                if (confirm(`Are you sure you want to delete the history file: ${filename}? This action cannot be undone.`)) {
                    clearMessage(historyMgmtMsgAreaId);
                    try {
                        const response = await fetch(`/api/admin/history/delete/${encodeURIComponent(filename)}`, { method: 'DELETE' });
                        const data = await response.json();
                        if (data.success) {
                            displayMessage(data.message, 'success', historyMgmtMsgAreaId);
                            fetchAndDisplayHistories();
                        } else {
                            displayMessage(data.message || "Failed to delete history.", 'error', historyMgmtMsgAreaId);
                        }
                    } catch (error) {
                        console.error("Error deleting history:", error);
                        displayMessage("Network error deleting history.", 'error', historyMgmtMsgAreaId);
                    }
                }
            });
        }

    }

    // --- User Portal Page ---
    if (pagePath.includes('/user-portal')) {
        const userPortalMsgAreaId = 'user-portal-message-area';
        adminUsernameForUserPortal = localStorage.getItem('admin_username_for_user') || 'Admin';


        const connectionIndicatorEl = document.getElementById('user-connection-status-indicator');
        const adminNameDisplayEl = document.getElementById('user-connection-admin-name');

        function updateUserConnectionStatus(isConnected, adminName = '') {
            if (connectionIndicatorEl) {
                connectionIndicatorEl.classList.remove('status-connected', 'status-disconnected', 'status-connecting');
                if (isConnected === null) { // Connecting state
                    connectionIndicatorEl.classList.add('status-connecting');
                    connectionIndicatorEl.title = 'Connecting...';
                    if(adminNameDisplayEl) adminNameDisplayEl.textContent = '';
                } else if (isConnected) {
                    connectionIndicatorEl.classList.add('status-connected');
                    connectionIndicatorEl.title = 'Connected';
                    if(adminNameDisplayEl) adminNameDisplayEl.textContent = `Connected`;
                } else {
                    connectionIndicatorEl.classList.add('status-disconnected');
                    connectionIndicatorEl.title = 'Disconnected';
                     if(adminNameDisplayEl) adminNameDisplayEl.textContent = '';
                }
            }
        }
        updateUserConnectionStatus(null); // Initial state: connecting


        if (typeof ONE_TIME_CODE_USER === 'undefined' || typeof ADMIN_SESSION_ID_FOR_USER === 'undefined') {
            displayMessage('Session information missing. Please try joining again.', 'error', userPortalMsgAreaId);
            updateUserConnectionStatus(false);
            return;
        }

        socket = io({ transports: ['polling'] });

        socket.on('connect', () => {
            console.log('User portal connected via SocketIO.');
            updateUserConnectionStatus(null); // Connecting
            socket.emit('join_user_to_admin_session', { code: ONE_TIME_CODE_USER, admin_session_id: ADMIN_SESSION_ID_FOR_USER });
        });
        socket.on('connect_error', (err) => {
            console.error('User portal SocketIO connection error:', err);
            displayMessage(`Connection Error: ${err.message}. Please try rejoining.`, 'error', userPortalMsgAreaId);
            updateUserConnectionStatus(false);
        });
        socket.on('user_join_success', (d) => {
            displayMessage(d.message, 'success', userPortalMsgAreaId);
            adminUsernameForUserPortal = d.admin_username || adminUsernameForUserPortal; // Update with server provided name if available
            localStorage.setItem('admin_username_for_user', adminUsernameForUserPortal); // Save for potential refresh
            updateUserConnectionStatus(true, adminUsernameForUserPortal);
        });
        socket.on('user_join_failed', (d) => { // Custom event for join failure
            displayMessage(`Failed to join: ${d.message}. You may need a new code.`, 'error', userPortalMsgAreaId);
            updateUserConnectionStatus(false);
             ['user-numbers-input', 'user-submit-numbers-button', 'user-undo-entry-button'].forEach(id => {
                const el = document.getElementById(id); if(el) el.disabled = true;
            });
        });
        socket.on('error_critical', (d) => { // General critical errors after joining
            displayMessage(`Critical Error: ${d.message}. Controls disabled.`, 'error', userPortalMsgAreaId);
            updateUserConnectionStatus(false);
            ['user-numbers-input', 'user-submit-numbers-button', 'user-undo-entry-button'].forEach(id => {
                const el = document.getElementById(id); if(el) el.disabled = true;
            });
        });
        socket.on('error_toast', (d) => displayMessage(d.message, 'error', userPortalMsgAreaId));
        socket.on('action_feedback', (d) => displayMessage(d.message, d.type || 'success', userPortalMsgAreaId));

        const userNumbersIn = document.getElementById('user-numbers-input');
        const userSubmitBtn = document.getElementById('user-submit-numbers-button');
        const userUndoBtn = document.getElementById('user-undo-entry-button');

        if (userSubmitBtn && userNumbersIn) userSubmitBtn.addEventListener('click', () => {
            const val = userNumbersIn.value.trim();
            if (val) {
                // Replace commas with hyphens if present
                const formattedVal = val.replace(/,/g, '-');
                socket.emit('user_submit_spin', { admin_session_id: ADMIN_SESSION_ID_FOR_USER, spin_value: formattedVal });
                userNumbersIn.value = '';
            } // 'user_submit_spin' is server event, 'spin_value' is param
            else displayMessage('Please enter a number or value.', 'error', userPortalMsgAreaId);
        });
        if (userNumbersIn) userNumbersIn.addEventListener('keypress', (e) => { if (e.key === 'Enter') { e.preventDefault(); if(userSubmitBtn) userSubmitBtn.click(); }});
        if (userUndoBtn) userUndoBtn.addEventListener('click', () => socket.emit('user_undo_last_spin', { admin_session_id: ADMIN_SESSION_ID_FOR_USER })); // 'user_undo_last_spin' is server event

        socket.on('disconnect', () => {
            displayMessage('Disconnected from admin session. Controls are disabled.', 'info', userPortalMsgAreaId);
            updateUserConnectionStatus(false);
            if(userNumbersIn) userNumbersIn.disabled = true;
            if(userSubmitBtn) userSubmitBtn.disabled = true;
            if(userUndoBtn) userUndoBtn.disabled = true;
        });

        // Handle admin terminating the session
        socket.on('admin_terminated_session', (data) => {
            displayMessage('Admin has ended the session. You will be disconnected.', 'error', userPortalMsgAreaId);
            updateUserConnectionStatus(false);
            if(userNumbersIn) userNumbersIn.disabled = true;
            if(userSubmitBtn) userSubmitBtn.disabled = true;
            if(userUndoBtn) userUndoBtn.disabled = true;

            // Automatically redirect to home page after a short delay
            setTimeout(() => {
                window.location.href = '/';
            }, 3000);
        });
    }
});

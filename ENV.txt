Here's how to do it:

Navigate to your project directory (if you're not already there):

Bash

cd ~/Desktop/Playground/APP_1
Create a virtual environment:
You can name your virtual environment anything you like; venv or .venv are common choices.

Bash

python3 -m venv .venv 
This command creates a folder named .venv (or venv if you used that) in your APP_1 directory. This folder will contain a copy of the Python interpreter and a place to install packages locally for this project.

Activate the virtual environment:
The command to activate depends on your shell. Since your prompt looks like a standard Linux bash prompt:

Bash

source .venv/bin/activate
After activation, your command prompt will usually change to show the name of the virtual environment, like (.venv) lastad@lastad-Surface-Laptop-Go-2:...$. This indicates that any python or pip commands will now use the versions within this virtual environment.

Install the packages using pip:
Now that your virtual environment is active, pip install will install packages into this isolated environment, not system-wide.

Bash

python -m pip install --upgrade pip

pip install -r requirements.txt
This should now work without the "externally-managed-environment" error.

Run your application:
While the virtual environment is active, run your Flask app as before:

Bash

python app.py
Deactivate the virtual environment (when you're done working on the project):
Simply type:

Bash

deactivate
Your prompt will return to normal.

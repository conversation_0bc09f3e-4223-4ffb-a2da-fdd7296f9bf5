<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Portal - Roulette Tracker</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.4/socket.io.min.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body class="user-portal-body">
    <div class="user-container card">
        <header class="portal-header">
            <h1>User Portal</h1>
            <div class="connection-status-container">
                <span>Status:</span>
                <div id="user-connection-status-indicator" class="connection-indicator status-disconnected" title="Disconnected"></div>
                <span id="user-connection-admin-name"></span>
            </div>
        </header>

        <section class="user-actions">
            <h2>Submit Your Numbers</h2>
            <div class="input-group">
                <label for="user-numbers-input">Number(s)</label>
                <input type="text" id="user-numbers-input" placeholder="e.g., 7-15-0">
            </div>
            <div class="button-group">
                <button id="user-submit-numbers-button" class="button-success">Submit Numbers</button>
                <button id="user-undo-entry-button" class="button-warning">Undo Last Entry</button>
            </div>
        </section>

        <div id="user-portal-message-area" class="message-box" style="margin-top:20px; display:none;"></div>
    </div>
    <script>
        // Pass data from Flask template to JavaScript
        const ONE_TIME_CODE_USER = "{{ one_time_code }}";
        const ADMIN_SESSION_ID_FOR_USER = "{{ admin_session_id }}";
        // Admin username is now passed from server on successful code validation (see main.js)
    </script>
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
</body>
</html>

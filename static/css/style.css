/* --- File: static/css/style.css --- */
:root {
    /* Primary color palette - orange-based theme */
    --primary-color: #FF6F61;      /* Main orange/coral */
    --secondary-color: #87CEEB;    /* Light blue */
    --accent-color: #FFD700;       /* Gold accent - used sparingly */

    /* Text colors */
    --text-color: #2c3e50;
    --text-light: #555e68;

    /* Background colors */
    --bg-color: #F4F7F6;
    --card-bg: #FFFFFF;
    --border-color: #E0E0E0;
    --input-border: #CFD8DC;

    /* Status colors - consistent with theme */
    --success-bg: #E6FFFA;
    --success-text: #00A78E;
    --error-bg: #FFF0F0;
    --error-text: #D8000C;
    --info-bg: #EBF8FF;
    --info-text: #007BFF;

    /* Status indicators */
    --status-connected-color: #28a745;    /* Green */
    --status-disconnected-color: #dc3545; /* Red */
    --status-connecting-color: #ffc107;   /* Amber */

    /* Typography and layout */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
    --border-radius: 8px;
    --box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    --box-shadow-hover: 0 6px 16px rgba(0, 0, 0, 0.08);
}

html {
    scroll-behavior: smooth;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

body {
    font-family: var(--font-family);
    margin: 0;
    background-color: var(--bg-color);
    color: var(--text-color);
    line-height: 1.65;
    font-size: 16px;
    overflow-x: hidden;
}

.auth-body, .user-portal-body, .admin-dashboard-body {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    min-height: 100vh;
    padding: 20px 15px;
    box-sizing: border-box;
}

.card {
    background-color: var(--card-bg);
    padding: 25px 30px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin-bottom: 20px;
    width: 100%;
    box-sizing: border-box;
}

.auth-container, .user-container {
    width: 100%;
    max-width: 450px;
    margin-top: clamp(20px, 8vh, 60px);
}
.admin-container {
    width: 100%;
    max-width: 1400px;
}


h1 {
    color: var(--primary-color);
    margin-bottom: 25px;
    font-size: clamp(1.8em, 5vw, 2.2em);
    font-weight: 700;
    text-align: center;
}
h2 {
    color: var(--text-light);
    margin-top: 0;
    margin-bottom: 20px;
    font-size: clamp(1.3em, 4vw, 1.6em);
    font-weight: 600;
    border-bottom: 2px solid var(--secondary-color);
    padding-bottom: 10px;
}
h3 {
    color: var(--text-light);
    margin-top: 0;
    margin-bottom: 15px;
    font-size: clamp(1.1em, 3.5vw, 1.3em);
    font-weight: 600;
}

label {
    display: block;
    margin-bottom: 8px;
    color: var(--text-light);
    font-weight: 500;
    font-size: 0.9em;
}

input[type="text"],
input[type="password"],
select {
    width: 100%;
    padding: 14px 18px;
    margin-bottom: 18px;
    border: 1px solid var(--input-border);
    border-radius: var(--border-radius);
    box-sizing: border-box;
    font-size: 1em;
    transition: border-color 0.2s, box-shadow 0.2s;
    background-color: #FCFCFC;
}
input[type="text"]:focus,
input[type="password"]:focus,
select:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 3px rgba(255, 111, 97, 0.25);
}
input::placeholder {
    color: #90A4AE;
}


.input-group {
    margin-bottom: 22px;
}

button, .button-primary, .button-secondary, .button-success, .button-warning, .button-danger, .button-tts {
    padding: 12px 20px;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 1em;
    font-weight: 600;
    transition: background-color 0.2s ease, transform 0.1s ease, box-shadow 0.2s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    line-height: 1.2;
}
button:hover, .button-primary:hover, .button-secondary:hover, .button-success:hover, .button-warning:hover, .button-danger:hover, .button-tts:hover {
    transform: translateY(-2px);
    box-shadow: var(--box-shadow-hover);
}
button:active, .button-primary:active, .button-secondary:active, .button-success:active, .button-warning:active, .button-danger:active, .button-tts:active {
    transform: translateY(0px);
    box-shadow: inset 0 2px 4px rgba(0,0,0,0.08);
}
button:disabled, .button-primary:disabled, .button-secondary:disabled, .button-success:disabled, .button-warning:disabled, .button-danger:disabled, .button-tts:disabled {
    background-color: #ccc;
    color: #666;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.button-primary {
    background-color: var(--primary-color);
    color: white;
}
.button-primary:hover:not(:disabled) {
    background-color: #E55A4D; /* Darker coral */
}
.button-secondary {
    background-color: var(--secondary-color);
    color: #2c3e50;
}
.button-secondary:hover:not(:disabled) {
    background-color: #70B8D8; /* Darker secondary blue */
}
.button-success {
    background-color: var(--success-text);
    color: white;
}
.button-success:hover:not(:disabled) {
    background-color: #008C73; /* Darker green */
}
.button-warning {
    background-color: #FFA500; /* Orange */
    color: white;
}
.button-warning:hover:not(:disabled) {
    background-color: #E59400; /* Darker orange */
}
.button-danger {
    background-color: var(--error-text);
    color: white;
}
.button-danger:hover:not(:disabled) {
    background-color: #B8000A; /* Darker red */
}
.button-tts {
    background-color: #6c757d;
    color: white;
    font-size: 0.85em;
    padding: 8px 14px;
}
.button-tts:hover:not(:disabled) {
    background-color: #5a6268;
}
.button-tts.active {
    background-color: var(--primary-color);
}
.small-button { padding: 8px 15px; font-size: 0.9em; }
.code-display-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    max-width: 200px; /* Limit width for better proportions */
    margin: 0 auto 15px auto;
}
#code-display-area {
    width: 100%;
    font-weight: bold;
    text-align: center;
    padding: 10px;
    min-height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}
.button-full-width { width: 100%; padding: 14px; }
.button-group { display: flex; flex-wrap: wrap; gap: 12px; margin-top: 18px; }
.button-group button { flex-grow: 1; min-width: 120px; }

.message-box {
    margin-top: 18px;
    padding: 12px 16px;
    border-radius: var(--border-radius);
    font-size: 0.95em;
    text-align: center;
    border: 1px solid transparent;
    display: none;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}
.message-box.success-box {
    background-color: var(--success-bg);
    color: var(--success-text);
    border-color: rgba(46, 125, 98, 0.3);
    display: block;
}
.message-box.error-box {
    background-color: var(--error-bg);
    color: var(--error-text);
    border-color: rgba(194, 57, 52, 0.3);
    display: block;
}
.message-box.info-box {
    background-color: var(--info-bg);
    color: var(--info-text);
    border-color: rgba(58, 110, 165, 0.3);
    display: block;
}

.auth-switch { text-align: center; margin-top: 25px; font-size: 0.9em; }
.auth-switch a { color: var(--primary-color); text-decoration: none; font-weight: 500; }
.auth-switch a:hover { text-decoration: underline; }

/* Admin Dashboard Specific */
.dashboard-header {
    display: flex; flex-wrap: wrap; justify-content: space-between; align-items: center;
    padding: 20px 25px; background-color: var(--card-bg); border-radius: var(--border-radius);
    box-shadow: var(--box-shadow); margin-bottom: 25px;
}
.dashboard-header div:first-child { margin-right: 15px;}
.dashboard-header h1 { font-size: clamp(1.5em, 4vw, 1.8em); margin-bottom: 5px; text-align: left;}
.dashboard-header p { margin: 3px 0; color: var(--text-light); font-size: 0.9em;}
.dashboard-header strong { color: var(--primary-color); }
#current-wheel-type-display { margin-left: 10px; vertical-align: middle;}

/* Mobile-first Focused View Layout */
.dashboard-focused-view {
    display: flex;
    flex-direction: column; /* Stack elements by default for mobile */
    gap: 15px;
    margin-bottom: 20px;
}
.dashboard-focused-view .card {
    margin-bottom: 0; /* Remove bottom margin for cards inside focused view */
    padding: 15px; /* Slightly smaller padding for mobile */
}
.dashboard-focused-view h2, .dashboard-focused-view h3 {
    margin-bottom: 12px; /* Smaller margins for headings */
    font-size: 1.1em; /* Smaller font size for mobile */
}

/* Compact Status Panel for Mobile */
.status-panel .status-grid {
    display: grid;
    grid-template-columns: 1fr; /* Single column by default for mobile */
    gap: 8px;
}
.status-panel .status-item {
    background-color: #f9f9f9;
    padding: 10px;
    border-radius: calc(var(--border-radius) - 4px);
    border: 1px solid var(--border-color);
}
/* Compact status items with inline elements */
.status-panel .status-item-compact {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    align-items: center;
}
.status-panel .status-item-compact span {
    margin-right: 8px;
}
.status-panel .status-text {
    color: var(--text-light);
    font-size: 0.9em; /* Smaller text for mobile */
}
.status-pill {
    padding: 4px 10px;
    border-radius: 15px;
    font-weight: 600;
    color: white;
    background-color: #7f8c8d;
    font-size: 0.85em;
    display: inline-block;
}
.status-pill.status-red { background-color: var(--error-text); }
.status-pill.status-yellow { background-color: #f39c12; }
.status-pill.status-green { background-color: var(--success-text); }
.status-pill.status-neutral { background-color: #6c757d; }

/* Distill Output Panels */
.dashboard-focused-view .output-panel pre {
    min-height: 80px; /* Smaller height for mobile */
    max-height: 150px; /* Limit height on mobile */
    overflow-y: auto; /* Add scrolling */
    font-size: 0.85em; /* Smaller font for mobile */
    padding: 12px;
}
.dashboard-focused-view .current-distill-panel {
    order: 1; /* Ensure Current Distill comes first */
}
.dashboard-focused-view .previous-distill-panel {
    order: 3; /* Previous Distill comes after TTS controls */
}

/* TTS Controls */
.dashboard-focused-view .tts-controls {
    order: 2; /* TTS controls between Current and Previous */
    padding-bottom: 10px; /* Less padding at bottom */
}
.tts-controls .tts-button-group {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center; /* Center buttons on mobile */
    gap: 8px;
    margin-bottom: 10px;
}
.tts-controls #tts-rate-display {
    font-size: 0.85em;
    color: var(--text-light);
    margin: 0 5px;
}
.tts-controls .tts-select-group {
    display: flex;
    align-items: center;
    justify-content: center; /* Center on mobile */
    gap: 8px;
    font-size: 0.85em;
}
.tts-controls select {
    padding: 6px 8px;
    border-radius: var(--border-radius);
    border: 1px solid var(--input-border);
    font-size: 0.9em;
    background-color: #FCFCFC;
}
.tts-controls .button-tts {
    padding: 6px 10px; /* Smaller padding for mobile */
    font-size: 0.8em; /* Smaller font for mobile */
}


/* Main Grid for other controls */
.dashboard-main-grid { display: grid; grid-template-columns: 320px 1fr; gap: 25px; }
.dashboard-column-left, .dashboard-column-right { display: flex; flex-direction: column; gap: 25px; }
.control-panel .input-group input[type="text"] { margin-bottom: 12px; }


.output-display .spin-list {
    list-style-type: none;
    padding: 15px;
    max-height: 280px;
    overflow-y: auto;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    margin-top: 10px;
    background-color: #fdfdfd;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
    gap: 8px;
}
.output-display .spin-list li {
    padding: 6px 10px;
    border: 1px solid #f0f0f0;
    border-radius: 4px;
    font-size: 0.95em;
    background-color: #f9f9f9;
    display: inline-block;
}

.output-panel pre { background-color: #fdfdfd; border: 1px solid var(--border-color); border-radius: var(--border-radius); padding: 18px; font-family: 'SF Mono', 'Courier New', Courier, monospace; font-size: 0.9em; white-space: pre-wrap; word-wrap: break-word; min-height: 120px; color: #455A64; }

.history-management-panel .history-controls { margin-bottom: 15px; }
.history-management-panel #history-view-area-container {
    margin-top: 15px; padding: 15px; background-color: #f9f9f9;
    border: 1px solid var(--border-color); border-radius: var(--border-radius);
}
.history-management-panel #history-view-area {
    white-space: pre-wrap; word-wrap: break-word; font-family: 'SF Mono', 'Courier New', Courier, monospace;
    font-size: 0.9em; max-height: 200px; overflow-y: auto; background-color: var(--card-bg);
    padding: 10px; border-radius: calc(var(--border-radius) - 4px); border: 1px solid var(--input-border);
}

/* User Portal Specific */
.user-portal-body .user-container { margin-top: clamp(30px, 10vh, 80px); }
.portal-header { text-align: center; margin-bottom: 25px; }
.portal-header h1 { font-size: clamp(1.6em, 4.5vw, 2em); }
.portal-header p { color: var(--text-light); font-size: 1em; } /* Removed for connection indicator */
.portal-header strong { color: var(--primary-color); }

.connection-status-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 8px 0;
    font-size: 0.95em;
    color: var(--text-light);
}
.connection-indicator {
    width: 14px;
    height: 14px;
    border-radius: 50%;
    background-color: var(--status-disconnected-color); /* Default to disconnected */
    transition: background-color 0.3s ease;
}
.connection-indicator.status-connected { background-color: var(--status-connected-color); }
.connection-indicator.status-disconnected { background-color: var(--status-disconnected-color); }
.connection-indicator.status-connecting { background-color: var(--status-connecting-color); }


.user-actions { text-align: center; }
.user-actions h2 { text-align: center; margin-left: auto; margin-right: auto; }
.user-actions .input-group { max-width: 320px; margin-left: auto; margin-right: auto; }


/* Responsive adjustments */
/* Desktop Layout (1025px and above) */
@media (min-width: 1025px) {
    /* Desktop layout for focused view - grid with 2 columns */
    .dashboard-focused-view {
        display: grid;
        grid-template-columns: 1fr 1fr; /* Two equal columns */
        grid-template-rows: auto auto; /* Two rows */
        gap: 20px;
    }

    /* Status panel spans full width in first row */
    .dashboard-focused-view .status-panel {
        grid-column: 1 / -1; /* Span all columns */
        grid-row: 1; /* First row */
    }

    /* Status panel has 3 columns on desktop */
    .status-panel .status-grid {
        grid-template-columns: repeat(3, 1fr); /* 3 columns for desktop */
        gap: 15px;
    }

    /* Current distill in second row, first column */
    .dashboard-focused-view .current-distill-panel {
        grid-column: 1; /* First column */
        grid-row: 2; /* Second row */
        order: unset; /* Reset order for grid layout */
    }

    /* TTS controls in second row, second column */
    .dashboard-focused-view .tts-controls {
        grid-column: 2; /* Second column */
        grid-row: 2; /* Second row */
        order: unset; /* Reset order for grid layout */
    }

    /* Previous distill in third row, spans full width */
    .dashboard-focused-view .previous-distill-panel {
        grid-column: 1 / -1; /* Span all columns */
        grid-row: 3; /* Third row */
        order: unset; /* Reset order for grid layout */
    }

    /* Restore padding and font sizes for desktop */
    .dashboard-focused-view .card {
        padding: 25px;
    }

    .dashboard-focused-view h2, .dashboard-focused-view h3 {
        font-size: 1.3em;
        margin-bottom: 20px;
    }

    .dashboard-focused-view .output-panel pre {
        min-height: 120px;
        max-height: 200px;
        font-size: 0.9em;
        padding: 18px;
    }

    .tts-controls .button-tts {
        padding: 8px 14px;
        font-size: 0.85em;
    }
}

/* Tablet Layout (768px to 1024px) */
@media (min-width: 768px) and (max-width: 1024px) {
    /* Tablet layout for focused view - 2 columns but different arrangement */
    .dashboard-focused-view {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 15px;
    }

    /* Status panel spans full width */
    .dashboard-focused-view .status-panel {
        grid-column: 1 / -1;
    }

    /* Status panel has 3 columns on tablet */
    .status-panel .status-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 12px;
    }

    /* Current distill in first column */
    .dashboard-focused-view .current-distill-panel {
        grid-column: 1;
        order: unset;
    }

    /* TTS controls in second column */
    .dashboard-focused-view .tts-controls {
        grid-column: 2;
        order: unset;
    }

    /* Previous distill spans full width */
    .dashboard-focused-view .previous-distill-panel {
        grid-column: 1 / -1;
        order: unset;
    }

    /* Main grid adjustments for tablet */
    .dashboard-main-grid {
        grid-template-columns: 280px 1fr;
    }
}

/* Small Tablet / Large Mobile (600px to 767px) */
@media (min-width: 600px) and (max-width: 767px) {
    /* Status panel has 2 columns on small tablet */
    .status-panel .status-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    /* Main grid becomes single column */
    .dashboard-main-grid {
        grid-template-columns: 1fr;
    }

    /* Header adjustments */
    .dashboard-header {
        align-items: flex-start;
    }

    .dashboard-header div:first-child {
        margin-bottom: 10px;
        width: 100%;
    }

    .dashboard-header .button-danger {
        margin-top: 5px;
        width: auto;
    }
}

/* Mobile Layout (up to 599px) */
@media (max-width: 599px) {
    body {
        font-size: 15px;
    }

    .auth-body, .user-portal-body, .admin-dashboard-body {
        padding: 15px 10px;
    }

    .auth-container, .user-container {
        max-width: 100%;
        padding: 15px;
    }

    .admin-container {
        padding: 10px;
    }

    .card {
        padding: 15px;
    }

    /* Header becomes fully stacked on mobile */
    .dashboard-header {
        flex-direction: column;
        align-items: center;
        text-align: center;
        padding: 15px;
    }

    .dashboard-header div:first-child {
        text-align: center;
    }

    .dashboard-header h1 {
        font-size: 1.4em;
    }

    .dashboard-header p {
        font-size: 0.85em;
    }

    .dashboard-header .button-danger {
        width: 100%;
        margin-top: 15px;
    }

    /* Button groups stack vertically on mobile */
    .button-group {
        flex-direction: column;
    }

    .button-group button {
        width: 100%;
    }

    /* Status panel stays as single column */
    .status-panel .status-grid {
        grid-template-columns: 1fr;
    }

    /* Form elements adjustments */
    input[type="text"], input[type="password"], button, .button-primary, select {
        font-size: 0.95em;
        padding: 12px 15px;
    }

    /* Headings size adjustments */
    h1 {
        font-size: 1.6em;
    }

    h2 {
        font-size: 1.2em;
    }

    h3 {
        font-size: 1.1em;
    }
}

Maintainer (i.e., Who To <PERSON>sle If You Find <PERSON>)
-------------------------------------------------

The current maintainer(s) are volunteers with unrelated jobs.
We can only pay sporadic attention to responding to your issue and pull request submissions.
Your patience is greatly appreciated!

Active maintainers
~~~~~~~~~~~~~~~~~~

* <PERSON><PERSON><PERSON> https://github.com/itamarst
* <PERSON> https://github.com/tipabu
* Hervé <PERSON> https://github.com/4383

Less active maintainers
~~~~~~~~~~~~~~~~~~~~~~~

* <PERSON> https://github.com/temoto
* Jakub <PERSON>ak https://github.com/jstasiak
* Nat Goodspeed https://github.com/nat-goodspeed

Original Authors
----------------
* <PERSON>
* <PERSON>

Contributors
------------
* AG <PERSON>
* <PERSON>
* R\. <PERSON>
* <PERSON>
* <PERSON>
* <PERSON>
* <PERSON>
* <PERSON>
* <PERSON><PERSON><PERSON>
* <PERSON>
* <PERSON>
* Chet <PERSON>
* <PERSON>
* radix
* <PERSON>
* <PERSON>
* <PERSON>
* <PERSON>
* <PERSON>
* <PERSON>e <PERSON>
* <PERSON>
* <PERSON>
* <PERSON>
* <PERSON>
* <PERSON>
* J<PERSON>
* <PERSON>
* <PERSON>
* <PERSON>
* <PERSON>inner
* <PERSON>
* <PERSON>
* <PERSON>
* <PERSON> Con<PERSON>
-----------------------
* <PERSON>
* <PERSON>
* <PERSON> Goodspeed
* Dave Kaprielian
* Kartic Krishnamurthy
* Bryan O'Sullivan
* Kent Quirk
* Ryan Williams

Thanks To
---------
* AdamKG, giving the hint that invalid argument errors were introduced post-0.9.0
* Luke Tucker, bug report regarding wsgi + webob
* Taso Du Val, reproing an exception squelching bug, saving children's lives  ;-)
* Luci Stanescu, for reporting twisted hub bug
* Marcus Cavanaugh, for test case code that has been incredibly useful in tracking down bugs
* Brian Brunswick, for many helpful questions and suggestions on the mailing list
* Cesar Alaniz, for uncovering bugs of great import
* the grugq, for contributing patches, suggestions, and use cases
* Ralf Schmitt, for wsgi/webob incompatibility bug report and suggested fix
* Benoit Chesneau, bug report on green.os and patch to fix it
* Slant, better iterator implementation in tpool
* Ambroff, nice pygtk hub example
* Michael Carter, websocket patch to improve location handling
* Marcin Bachry, nice repro of a bug and good diagnosis leading to the fix
* David Ziegler, reporting issue #53
* Favo Yang, twisted hub patch
* Schmir, patch that fixes readline method with chunked encoding in wsgi.py, advice on patcher
* Slide, for open-sourcing gogreen
* Holger Krekel, websocket example small fix
* mikepk, debugging MySQLdb/tpool issues
* Malcolm Cleaton, patch for Event exception handling
* Alexey Borzenkov, for finding and fixing issues with Windows error detection (#66, #69), reducing dependencies in zeromq hub (#71)
* Anonymous, finding and fixing error in websocket chat example (#70)
* Edward George, finding and fixing an issue in the [e]poll hubs (#74), and in convenience (#86)
* Ruijun Luo, figuring out incorrect openssl import for wrap_ssl (#73)
* rfk, patch to get green zmq to respect noblock flag.
* Soren Hansen, finding and fixing issue in subprocess (#77)
* Stefano Rivera, making tests pass in absence of postgres (#78)
* Joshua Kwan, fixing busy-wait in eventlet.green.ssl.
* Nick Vatamaniuc, Windows SO_REUSEADDR patch (#83)
* Clay Gerrard, wsgi handle socket closed by client (#95)
* Eric Windisch, zmq getsockopt(EVENTS) wake correct threads (pull request 22)
* Raymond Lu, fixing busy-wait in eventlet.green.ssl.socket.sendall()
* Thomas Grainger, webcrawler example small fix, "requests" library import bug report, Travis integration
* Peter Portante, save syscalls in socket.dup(), environ[REMOTE_PORT] in wsgi
* Peter Skirko, fixing socket.settimeout(0) bug
* Derk Tegeler, Pre-cache proxied GreenSocket methods (Bitbucket #136)
* David Malcolm, optional "timeout" argument to the subprocess module (Bitbucket #89)
* David Goetz, wsgi: Allow minimum_chunk_size to be overriden on a per request basis
* Dmitry Orlov, websocket: accept Upgrade: websocket (lowercase)
* Zhang Hua, profile: accumulate results between runs (Bitbucket #162)
* Astrum Kuo, python3 compatibility fixes; greenthread.unlink() method
* Davanum Srinivas, Python3 compatibility fixes
* Dmitriy Kruglyak, PyPy 2.3 compatibility fix
* Jan Grant, Michael Kerrin, second simultaneous read (GH-94)
* Simon Jagoe, Python3 octal literal fix
* Tushar Gohad, wsgi: Support optional headers w/ "100 Continue" responses
* raylu, fixing operator precedence bug in eventlet.wsgi
* Christoph Gysin, PEP 8 conformance
* Andrey Gubarev
* Corey Wright
* Deva
* Johannes Erdfelt
* Kevin
* QthCN
* Steven Hardy
* Stuart McLaren
* Tomaz Muraus
* ChangBo Guo(gcb), fixing typos in the documentation (GH-194)
* Marc Abramowitz, fixing the README so it renders correctly on PyPI (GH-183)
* Shaun Stanworth, equal chance to acquire semaphore from different greenthreads (GH-136)
* Lior Neudorfer, Make sure SSL retries are done using the exact same data buffer
* Sean Dague, wsgi: Provide python logging compatibility
* Tim Simmons, Use _socket_nodns and select in dnspython support
* Antonio Cuni, fix fd double close on PyPy
* Seyeong Kim
* Ihar Hrachyshka
* Janusz Harkot
* Fukuchi Daisuke
* Ramakrishnan G
* ashutosh-mishra
* Azhar Hussain
* Josh VanderLinden
* Levente Polyak
* Phus Lu
* Collin Stocks, fixing eventlet.green.urllib2.urlopen() so it accepts cafile, capath, or cadefault arguments
* Alexis Lee
* Steven Erenst
* Piët Delport
* Alex Villacís Lasso
* Yashwardhan Singh
* Tim Burke
* Ondřej Nový
* Jarrod Johnson
* Whitney Young
* Matthew D. Pagel
* Matt Yule-Bennett
* Artur Stawiarski
* Tal Wrii
* Roman Podoliaka
* Gevorg Davoian
* Ondřej Kobližek
* Yuichi Bando
* Feng
* Aayush Kasurde
* Linbing
* Geoffrey Thomas
* Costas Christofi, adding permessage-deflate weboscket extension support
* Peter Kovary, adding permessage-deflate weboscket extension support
* Konstantin Enchant
* James Page
* Stefan Nica
* Haikel Guemar
* Miguel Grinberg
* Chris Kerr
* Anthony Sottile
* Quan Tian
* orishoshan
* Matt Bennett
* Ralf Haferkamp
* Jake Tesler
* Aayush Kasurde

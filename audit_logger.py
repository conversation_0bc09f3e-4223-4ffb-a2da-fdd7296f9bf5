# --- File: audit_logger.py ---
import logging
import logging.handlers
import os
from datetime import datetime
import json # Ensure json is imported

LOG_DIR = 'logs'
LOG_FILE = os.path.join(LOG_DIR, 'audit.log')

# Ensure log directory exists
os.makedirs(LOG_DIR, exist_ok=True)

# Configure logger
logger = logging.getLogger('audit')
logger.setLevel(logging.INFO)

# Prevent duplicate handlers if this module is reloaded (e.g., in some dev environments)
if not logger.hasHandlers():
    # Rotating File Handler
    # 10MB per file, 5 backup files
    rfh = logging.handlers.RotatingFileHandler(
        LOG_FILE,
        maxBytes=10*1024*1024, # 10 MB
        backupCount=5,
        encoding='utf-8' # Specify encoding
    )
    # More detailed formatter
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(name)s - %(module)s.%(funcName)s:%(lineno)d - %(message)s')
    rfh.setFormatter(formatter)
    logger.addHandler(rfh)

    # Optional: Console handler for development (can be controlled by app config)
    # console_handler = logging.StreamHandler()
    # console_handler.setFormatter(formatter)
    # logger.addHandler(console_handler)

def log_event(event_type, user_id, ip_address, status, request_details=None, details=None):
    """
    Logs an audit event.

    Args:
        event_type (str): e.g., "security.login", "user.join_session"
        user_id (str): Admin username or temporary user ID.
        ip_address (str): Requesting IP address.
        status (str): "success" or "failure".
        request_details (dict, optional): {'path': str, 'method': str, 'user_agent': str}.
        details (dict, optional): Additional context-specific info.
    """
    log_message_dict = {
        "timestamp": datetime.utcnow().isoformat() + "Z",
        "event_type": event_type,
        "user_id": user_id,
        "ip_address": ip_address,
        "status": status,
    }
    if request_details:
        log_message_dict["request_details"] = request_details
    if details:
        log_message_dict["additional_details"] = details

    try:
        log_string = json.dumps(log_message_dict)
        logger.info(log_string)
    except Exception as e:
        # Fallback if JSON serialization fails for some reason
        logger.error(f"Failed to format log message as JSON: {e} - Message: {str(log_message_dict)}")
        logger.info(str(log_message_dict)) # Log as string


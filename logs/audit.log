2025-05-14 04:06:56,916 - INFO - {'timestamp': '2025-05-14T09:06:56.916801Z', 'event_type': 'security.login', 'user_id': 'admin', 'ip_address': '127.0.0.1', 'status': 'success', 'request_details': {'path': '/admin-login', 'method': 'POST', 'user_agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'}, 'additional_details': {'session_id': '1b1c612e061119c40f4b4298aa30d98f'}}
2025-05-14 04:06:57,670 - INFO - {'timestamp': '2025-05-14T09:06:57.670911Z', 'event_type': 'admin.join_admin_socket', 'user_id': 'admin', 'ip_address': '127.0.0.1', 'status': 'success', 'additional_details': {'session_id': '1b1c612e061119c40f4b4298aa30d98f', 'sid': 'CMjOF3e49RfnQcJJAAAB'}}
2025-05-14 04:29:11,827 - INFO - audit - audit_logger.log_event:62 - {"timestamp": "2025-05-14T09:29:11.826990Z", "event_type": "security.login", "user_id": "admin", "ip_address": "127.0.0.1", "status": "success", "request_details": {"path": "/admin-login", "method": "POST", "user_agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, "additional_details": {"session_id": "ce43620a5b8c4d13f5288ebce5f5b0b3"}}
2025-05-14 04:29:12,137 - INFO - audit - audit_logger.log_event:62 - {"timestamp": "2025-05-14T09:29:12.137723Z", "event_type": "admin.join_admin_socket", "user_id": "admin", "ip_address": "127.0.0.1", "status": "success", "additional_details": {"session_id": "ce43620a5b8c4d13f5288ebce5f5b0b3", "sid": "2WJvh02RKGPf81ObAAAD"}}
2025-05-14 05:00:00,522 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-14T10:00:00.522088Z", "event_type": "sec.login", "user_id": "admin", "ip_address": "127.0.0.1", "status": "ok", "request_details": {"path": "/admin-login", "method": "POST", "user_agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, "additional_details": {"s_id": "44f71b3a69a17f6a98993d0c7625bf07"}}
2025-05-14 05:00:00,766 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-14T10:00:00.766041Z", "event_type": "admin.join_socket", "user_id": "admin", "ip_address": "127.0.0.1", "status": "ok", "additional_details": {"s_id": "44f71b3a69a17f6a98993d0c7625bf07", "sid": "SIwZSgdZUY8P7R-LAAAB"}}
2025-05-14 05:00:15,641 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-14T10:00:15.641187Z", "event_type": "admin.gen_code", "user_id": "admin", "ip_address": "127.0.0.1", "status": "ok", "request_details": {"path": "/api/generate-code", "method": "POST", "user_agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, "additional_details": {"code": "E8R9XG3X", "s_id": "44f71b3a69a17f6a98993d0c7625bf07"}}
2025-05-14 05:00:55,700 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-14T10:00:55.699997Z", "event_type": "user.val_code", "user_id": "N/A", "ip_address": "127.0.0.1", "status": "ok", "request_details": {"path": "/api/validate-code", "method": "POST", "user_agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, "additional_details": {"code": "E8R9XG3X", "admin_s_id": "44f71b3a69a17f6a98993d0c7625bf07"}}
2025-05-14 05:00:55,908 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-14T10:00:55.908533Z", "event_type": "user.join_s_socket", "user_id": "User_f62aad", "ip_address": "127.0.0.1", "status": "ok", "additional_details": {"code": "E8R9XG3X", "admin_s_id": "44f71b3a69a17f6a98993d0c7625bf07", "admin_user": "admin"}}
2025-05-14 05:01:08,423 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-14T10:01:08.422955Z", "event_type": "user.submit_spin", "user_id": "User_f62aad", "ip_address": "127.0.0.1", "status": "ok", "additional_details": {"spin": "3", "admin_s_id": "44f71b3a69a17f6a98993d0c7625bf07"}}
2025-05-14 05:01:09,709 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-14T10:01:09.709227Z", "event_type": "user.submit_spin", "user_id": "User_f62aad", "ip_address": "127.0.0.1", "status": "ok", "additional_details": {"spin": "5", "admin_s_id": "44f71b3a69a17f6a98993d0c7625bf07"}}
2025-05-14 05:01:15,998 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-14T10:01:15.998064Z", "event_type": "user.submit_spin", "user_id": "User_f62aad", "ip_address": "127.0.0.1", "status": "ok", "additional_details": {"spin": "17", "admin_s_id": "44f71b3a69a17f6a98993d0c7625bf07"}}
2025-05-15 16:04:36,697 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-15T21:04:36.697778Z", "event_type": "sec.login", "user_id": "admin", "ip_address": "127.0.0.1", "status": "ok", "request_details": {"path": "/admin-login", "method": "POST", "user_agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, "additional_details": {"s_id": "b696eedc14b5b1f161a37b1a6f410ccd"}}
2025-05-15 16:04:36,905 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-15T21:04:36.905657Z", "event_type": "admin.join_socket", "user_id": "admin", "ip_address": "127.0.0.1", "status": "ok", "additional_details": {"s_id": "b696eedc14b5b1f161a37b1a6f410ccd", "sid": "O5bo9sSdQb27p9A4AAAB"}}
2025-05-15 16:06:39,608 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-15T21:06:39.608317Z", "event_type": "admin.gen_code", "user_id": "admin", "ip_address": "127.0.0.1", "status": "ok", "request_details": {"path": "/api/generate-code", "method": "POST", "user_agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, "additional_details": {"code": "12ZF8G47", "s_id": "b696eedc14b5b1f161a37b1a6f410ccd"}}
2025-05-15 16:06:47,812 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-15T21:06:47.811968Z", "event_type": "admin.gen_code", "user_id": "admin", "ip_address": "127.0.0.1", "status": "ok", "request_details": {"path": "/api/generate-code", "method": "POST", "user_agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, "additional_details": {"code": "66A8MCM8", "s_id": "b696eedc14b5b1f161a37b1a6f410ccd"}}
2025-05-15 16:06:55,789 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-15T21:06:55.789807Z", "event_type": "user.val_code", "user_id": "N/A", "ip_address": "127.0.0.1", "status": "ok", "request_details": {"path": "/api/validate-code", "method": "POST", "user_agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, "additional_details": {"code": "66A8MCM8", "admin_s_id": "b696eedc14b5b1f161a37b1a6f410ccd"}}
2025-05-15 16:06:55,871 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-15T21:06:55.871298Z", "event_type": "user.join_s_socket", "user_id": "User_4f5242", "ip_address": "127.0.0.1", "status": "ok", "additional_details": {"code": "66A8MCM8", "admin_s_id": "b696eedc14b5b1f161a37b1a6f410ccd", "admin_user": "admin"}}
2025-05-15 16:13:37,034 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-15T21:13:37.034620Z", "event_type": "user.dc_session", "user_id": "User_4f5242", "ip_address": "127.0.0.1", "status": "ok", "additional_details": {"s_id": "b696eedc14b5b1f161a37b1a6f410ccd", "sid": "2mvUKMiLA9_MhGtrAAAD"}}
2025-05-15 16:13:48,351 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-15T21:13:48.351878Z", "event_type": "sec.login", "user_id": "admin", "ip_address": "*************", "status": "ok", "request_details": {"path": "/admin-login", "method": "POST", "user_agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 18_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.4 Mobile/15E148 Safari/604.1"}, "additional_details": {"s_id": "565009c4f0e5f4366ba522c5cbc512f2"}}
2025-05-15 16:13:48,757 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-15T21:13:48.757721Z", "event_type": "admin.join_socket", "user_id": "admin", "ip_address": "*************", "status": "ok", "additional_details": {"s_id": "565009c4f0e5f4366ba522c5cbc512f2", "sid": "GgdUmbaLAvHSxoNvAAAF"}}
2025-05-15 16:16:40,701 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-15T21:16:40.701531Z", "event_type": "sec.login", "user_id": "admin", "ip_address": "*************", "status": "ok", "request_details": {"path": "/admin-login", "method": "POST", "user_agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 18_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.4 Mobile/15E148 Safari/604.1"}, "additional_details": {"s_id": "9a895d7082e31bc376d34fe49fd84769"}}
2025-05-15 16:16:40,858 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-15T21:16:40.858116Z", "event_type": "admin.join_socket", "user_id": "admin", "ip_address": "*************", "status": "ok", "additional_details": {"s_id": "9a895d7082e31bc376d34fe49fd84769", "sid": "TnT19X3IXaFhTuA_AAAH"}}
2025-05-15 16:21:12,452 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-15T21:21:12.452008Z", "event_type": "admin.join_socket", "user_id": "admin", "ip_address": "*************", "status": "ok", "additional_details": {"s_id": "9a895d7082e31bc376d34fe49fd84769", "sid": "NtPESuQ7jgueeHS7AAAJ"}}
2025-05-15 16:25:55,480 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-15T21:25:55.480123Z", "event_type": "admin.join_socket", "user_id": "admin", "ip_address": "*************", "status": "ok", "additional_details": {"s_id": "9a895d7082e31bc376d34fe49fd84769", "sid": "LJkNNWC1PkGDglxMAAAL"}}
2025-05-15 16:43:05,806 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-15T21:43:05.806854Z", "event_type": "admin.join_socket", "user_id": "admin", "ip_address": "*************", "status": "ok", "additional_details": {"s_id": "9a895d7082e31bc376d34fe49fd84769", "sid": "aKGseVhTbtfzmi07AAAN"}}
2025-05-15 16:45:42,369 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-15T21:45:42.369117Z", "event_type": "admin.join_socket", "user_id": "admin", "ip_address": "*************", "status": "ok", "additional_details": {"s_id": "9a895d7082e31bc376d34fe49fd84769", "sid": "JsfQfyLosD7QS2-oAAAP"}}
2025-05-15 17:33:27,885 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-15T22:33:27.885056Z", "event_type": "sec.login", "user_id": "admin", "ip_address": "127.0.0.1", "status": "ok", "request_details": {"path": "/admin-login", "method": "POST", "user_agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, "additional_details": {"s_id": "3b9863ccac728bf1d8b48e985c12033c"}}
2025-05-15 17:33:27,982 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-15T22:33:27.981998Z", "event_type": "admin.join_socket", "user_id": "admin", "ip_address": "127.0.0.1", "status": "ok", "additional_details": {"s_id": "3b9863ccac728bf1d8b48e985c12033c", "sid": "nmRDSPR3kG1NjrQBAAAB"}}
2025-05-15 17:34:51,376 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-15T22:34:51.376514Z", "event_type": "sec.login", "user_id": "admin", "ip_address": "*************", "status": "ok", "request_details": {"path": "/admin-login", "method": "POST", "user_agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 18_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.4 Mobile/15E148 Safari/604.1"}, "additional_details": {"s_id": "dd62c0784090facea8f327c25be0f2f3"}}
2025-05-15 17:34:51,464 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-15T22:34:51.464091Z", "event_type": "admin.join_socket", "user_id": "admin", "ip_address": "*************", "status": "ok", "additional_details": {"s_id": "dd62c0784090facea8f327c25be0f2f3", "sid": "76inNHEEeZVFipNGAAAF"}}
2025-05-15 17:45:18,112 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-15T22:45:18.112141Z", "event_type": "sec.login", "user_id": "admin", "ip_address": "*************", "status": "ok", "request_details": {"path": "/admin-login", "method": "POST", "user_agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, "additional_details": {"s_id": "********************************"}}
2025-05-15 17:45:18,299 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-15T22:45:18.299605Z", "event_type": "admin.join_socket", "user_id": "admin", "ip_address": "*************", "status": "ok", "additional_details": {"s_id": "********************************", "sid": "JuP5qc9ms1G_Izq5AAAH"}}
2025-05-15 17:45:47,683 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-15T22:45:47.683363Z", "event_type": "admin.gen_code", "user_id": "admin", "ip_address": "*************", "status": "ok", "request_details": {"path": "/api/generate-code", "method": "POST", "user_agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, "additional_details": {"code": "J2591U8T", "s_id": "********************************"}}
2025-05-15 17:45:58,260 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-15T22:45:58.260719Z", "event_type": "user.val_code", "user_id": "N/A", "ip_address": "*************", "status": "fail", "request_details": {"path": "/api/validate-code", "method": "POST", "user_agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, "additional_details": {"code": "2591U8T", "reason": "Not found"}}
2025-05-15 17:46:02,388 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-15T22:46:02.388221Z", "event_type": "admin.gen_code", "user_id": "admin", "ip_address": "*************", "status": "ok", "request_details": {"path": "/api/generate-code", "method": "POST", "user_agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, "additional_details": {"code": "Z9GKU0GV", "s_id": "********************************"}}
2025-05-15 17:46:14,110 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-15T22:46:14.110643Z", "event_type": "user.val_code", "user_id": "N/A", "ip_address": "*************", "status": "ok", "request_details": {"path": "/api/validate-code", "method": "POST", "user_agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, "additional_details": {"code": "Z9GKU0GV", "admin_s_id": "********************************"}}
2025-05-15 17:46:14,222 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-15T22:46:14.222879Z", "event_type": "user.join_s_socket", "user_id": "User_944c7d", "ip_address": "*************", "status": "ok", "additional_details": {"code": "Z9GKU0GV", "admin_s_id": "********************************", "admin_user": "admin"}}
2025-05-15 17:46:26,063 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-15T22:46:26.063237Z", "event_type": "system.session_cleanup", "user_id": "admin", "ip_address": "N/A", "status": "success", "additional_details": {"s_id": "********************************", "reason": "logout"}}
2025-05-15 17:46:26,064 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-15T22:46:26.064566Z", "event_type": "sec.logout", "user_id": "admin", "ip_address": "*************", "status": "ok", "request_details": {"path": "/api/logout", "method": "POST", "user_agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, "additional_details": {"s_id": "********************************"}}
2025-05-15 17:46:31,813 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-15T22:46:31.813352Z", "event_type": "user.join_socket", "user_id": "N/A", "ip_address": "*************", "status": "fail", "additional_details": {"code": "Z9GKU0GV", "reason": "Invalid code socket join"}}
2025-05-17 03:51:47,747 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T08:51:47.747861Z", "event_type": "sec.login", "user_id": "admin", "ip_address": "127.0.0.1", "status": "ok", "request_details": {"path": "/admin-login", "method": "POST", "user_agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, "additional_details": {"s_id": "92649d931a4fc7b3773e1ffe9be7f3ee"}}
2025-05-17 03:52:14,079 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T08:52:14.079766Z", "event_type": "admin.gen_code", "user_id": "admin", "ip_address": "127.0.0.1", "status": "ok", "request_details": {"path": "/api/generate-code", "method": "POST", "user_agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, "additional_details": {"code": "1AHGZHBX", "s_id": "92649d931a4fc7b3773e1ffe9be7f3ee"}}
2025-05-17 03:52:27,451 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T08:52:27.451404Z", "event_type": "admin.gen_code", "user_id": "admin", "ip_address": "127.0.0.1", "status": "ok", "request_details": {"path": "/api/generate-code", "method": "POST", "user_agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, "additional_details": {"code": "GI3PBR45", "s_id": "92649d931a4fc7b3773e1ffe9be7f3ee"}}
2025-05-17 03:54:53,479 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T08:54:53.479104Z", "event_type": "sec.login", "user_id": "admin", "ip_address": "127.0.0.1", "status": "ok", "request_details": {"path": "/admin-login", "method": "POST", "user_agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, "additional_details": {"s_id": "64fd350a9e1829749ba0ba865040a10f"}}
2025-05-17 03:54:53,731 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T08:54:53.731586Z", "event_type": "admin.join_socket", "user_id": "admin", "ip_address": "127.0.0.1", "status": "ok", "additional_details": {"s_id": "64fd350a9e1829749ba0ba865040a10f", "sid": "CX_V6cxpDu3xWLJNAAAI"}}
2025-05-17 03:54:56,766 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T08:54:56.766721Z", "event_type": "admin.gen_code", "user_id": "admin", "ip_address": "127.0.0.1", "status": "ok", "request_details": {"path": "/api/generate-code", "method": "POST", "user_agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, "additional_details": {"code": "R7U1ABO0", "s_id": "64fd350a9e1829749ba0ba865040a10f"}}
2025-05-17 03:58:37,050 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T08:58:37.050067Z", "event_type": "sec.login", "user_id": "admin", "ip_address": "127.0.0.1", "status": "ok", "request_details": {"path": "/admin-login", "method": "POST", "user_agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, "additional_details": {"s_id": "60ab0a99251d034e3317fa1a0f5c20a4"}}
2025-05-17 03:58:37,336 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T08:58:37.335992Z", "event_type": "admin.join_socket", "user_id": "admin", "ip_address": "127.0.0.1", "status": "ok", "additional_details": {"s_id": "60ab0a99251d034e3317fa1a0f5c20a4", "sid": "icyFyrMrd2MISO5KAAAD"}}
2025-05-17 03:58:42,727 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T08:58:42.727170Z", "event_type": "admin.gen_code", "user_id": "admin", "ip_address": "127.0.0.1", "status": "ok", "request_details": {"path": "/api/generate-code", "method": "POST", "user_agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, "additional_details": {"code": "E568WYWN", "s_id": "60ab0a99251d034e3317fa1a0f5c20a4"}}
2025-05-17 03:58:50,877 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T08:58:50.876509Z", "event_type": "admin.submit_number", "user_id": "admin", "ip_address": "127.0.0.1", "status": "ok", "additional_details": {"number": "3", "s_id": "60ab0a99251d034e3317fa1a0f5c20a4", "wheel": "american"}}
2025-05-17 04:15:01,149 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T09:15:01.149114Z", "event_type": "sec.login", "user_id": "admin", "ip_address": "127.0.0.1", "status": "ok", "request_details": {"path": "/admin-login", "method": "POST", "user_agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, "additional_details": {"s_id": "2252e5b5a8b9f2ef5603c761726e70f8"}}
2025-05-17 04:15:01,357 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T09:15:01.357342Z", "event_type": "admin.join_socket", "user_id": "admin", "ip_address": "127.0.0.1", "status": "ok", "additional_details": {"s_id": "2252e5b5a8b9f2ef5603c761726e70f8", "sid": "O1irQBTxl2gfyVoIAAAB"}}
2025-05-17 04:20:43,029 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T09:20:43.028963Z", "event_type": "admin.gen_code", "user_id": "admin", "ip_address": "127.0.0.1", "status": "ok", "request_details": {"path": "/api/generate-code", "method": "POST", "user_agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, "additional_details": {"code": "EQ8MLP70", "s_id": "2252e5b5a8b9f2ef5603c761726e70f8"}}
2025-05-17 04:21:11,865 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T09:21:11.865202Z", "event_type": "admin.submit_number", "user_id": "admin", "ip_address": "127.0.0.1", "status": "fail", "additional_details": {"number": "8-33-10", "err": "Invalid number: '8-33-10'. Valid for American wheel: 00, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36", "s_id": "2252e5b5a8b9f2ef5603c761726e70f8", "wheel": "american"}}
2025-05-17 04:31:47,909 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T09:31:47.909005Z", "event_type": "sec.login", "user_id": "admin", "ip_address": "127.0.0.1", "status": "ok", "request_details": {"path": "/admin-login", "method": "POST", "user_agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, "additional_details": {"s_id": "e85e1e1137e90a7b8dd9ad48d286d7aa"}}
2025-05-17 04:31:48,256 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T09:31:48.256745Z", "event_type": "admin.join_socket", "user_id": "admin", "ip_address": "127.0.0.1", "status": "ok", "additional_details": {"s_id": "e85e1e1137e90a7b8dd9ad48d286d7aa", "sid": "hUw7ZDqB3TzwWoPcAAAD"}}
2025-05-17 04:31:53,928 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T09:31:53.928274Z", "event_type": "admin.gen_code", "user_id": "admin", "ip_address": "127.0.0.1", "status": "ok", "request_details": {"path": "/api/generate-code", "method": "POST", "user_agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, "additional_details": {"code": "HHJTXHU0", "s_id": "e85e1e1137e90a7b8dd9ad48d286d7aa"}}
2025-05-17 04:32:17,148 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T09:32:17.148823Z", "event_type": "user.val_code", "user_id": "N/A", "ip_address": "127.0.0.1", "status": "ok", "request_details": {"path": "/api/validate-code", "method": "POST", "user_agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, "additional_details": {"code": "HHJTXHU0", "admin_s_id": "e85e1e1137e90a7b8dd9ad48d286d7aa"}}
2025-05-17 04:32:17,418 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T09:32:17.418131Z", "event_type": "user.join_s_socket", "user_id": "User_792e5b", "ip_address": "127.0.0.1", "status": "ok", "additional_details": {"code": "HHJTXHU0", "admin_s_id": "e85e1e1137e90a7b8dd9ad48d286d7aa", "admin_user": "admin"}}
2025-05-17 04:32:31,899 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T09:32:31.899420Z", "event_type": "user.submit_number", "user_id": "User_792e5b", "ip_address": "127.0.0.1", "status": "fail", "additional_details": {"number": "9-8-18", "err": "Invalid number: '9-8-18'. Valid for American wheel: 0, 00, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36", "admin_s_id": "e85e1e1137e90a7b8dd9ad48d286d7aa", "wheel": "american"}}
2025-05-17 04:37:25,099 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T09:37:25.099157Z", "event_type": "user.join_socket", "user_id": "N/A", "ip_address": "127.0.0.1", "status": "fail", "additional_details": {"code": "HHJTXHU0", "reason": "Invalid code socket join"}}
2025-05-17 04:37:37,099 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T09:37:37.099128Z", "event_type": "user.join_socket", "user_id": "N/A", "ip_address": "127.0.0.1", "status": "fail", "additional_details": {"code": "HHJTXHU0", "reason": "Invalid code socket join"}}
2025-05-17 04:38:00,097 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T09:38:00.097854Z", "event_type": "user.join_socket", "user_id": "N/A", "ip_address": "127.0.0.1", "status": "fail", "additional_details": {"code": "HHJTXHU0", "reason": "Invalid code socket join"}}
2025-05-17 04:38:28,777 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T09:38:28.777010Z", "event_type": "sec.login", "user_id": "admin", "ip_address": "127.0.0.1", "status": "ok", "request_details": {"path": "/admin-login", "method": "POST", "user_agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, "additional_details": {"s_id": "f4561e20cfaee01e6128b4842161ddbf"}}
2025-05-17 04:38:29,003 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T09:38:29.003575Z", "event_type": "admin.join_socket", "user_id": "admin", "ip_address": "127.0.0.1", "status": "ok", "additional_details": {"s_id": "f4561e20cfaee01e6128b4842161ddbf", "sid": "TlNPwUz05OZUX5hWAAAD"}}
2025-05-17 04:41:11,775 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T09:41:11.775834Z", "event_type": "sec.login", "user_id": "admin", "ip_address": "127.0.0.1", "status": "ok", "request_details": {"path": "/admin-login", "method": "POST", "user_agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, "additional_details": {"s_id": "fbd42e84692a40ea60f6935c947067da"}}
2025-05-17 04:41:12,022 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T09:41:12.021965Z", "event_type": "admin.join_socket", "user_id": "admin", "ip_address": "127.0.0.1", "status": "ok", "additional_details": {"s_id": "fbd42e84692a40ea60f6935c947067da", "sid": "lM9mvFkLP4V7FL7dAAAD"}}
2025-05-17 04:41:17,106 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T09:41:17.106048Z", "event_type": "admin.gen_code", "user_id": "admin", "ip_address": "127.0.0.1", "status": "ok", "request_details": {"path": "/api/generate-code", "method": "POST", "user_agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, "additional_details": {"code": "332LPQRI", "s_id": "fbd42e84692a40ea60f6935c947067da"}}
2025-05-17 04:41:32,185 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T09:41:32.184966Z", "event_type": "user.val_code", "user_id": "N/A", "ip_address": "127.0.0.1", "status": "ok", "request_details": {"path": "/api/validate-code", "method": "POST", "user_agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, "additional_details": {"code": "332LPQRI", "admin_s_id": "fbd42e84692a40ea60f6935c947067da"}}
2025-05-17 04:41:32,406 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T09:41:32.406733Z", "event_type": "user.join_s_socket", "user_id": "User_e360c8", "ip_address": "127.0.0.1", "status": "ok", "additional_details": {"code": "332LPQRI", "admin_s_id": "fbd42e84692a40ea60f6935c947067da", "admin_user": "admin"}}
2025-05-17 04:43:21,250 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T09:43:21.250553Z", "event_type": "admin.gen_code", "user_id": "admin", "ip_address": "127.0.0.1", "status": "ok", "request_details": {"path": "/api/generate-code", "method": "POST", "user_agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, "additional_details": {"code": "J941MAPC", "s_id": "fbd42e84692a40ea60f6935c947067da"}}
2025-05-17 04:47:35,125 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T09:47:35.125908Z", "event_type": "user.join_socket", "user_id": "N/A", "ip_address": "127.0.0.1", "status": "fail", "additional_details": {"code": "332LPQRI", "reason": "Invalid code socket join"}}
2025-05-17 04:48:10,296 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T09:48:10.296665Z", "event_type": "sec.login", "user_id": "admin", "ip_address": "127.0.0.1", "status": "ok", "request_details": {"path": "/admin-login", "method": "POST", "user_agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, "additional_details": {"s_id": "f6f6b911ceecd84268b8e8b2aea4c787"}}
2025-05-17 04:48:10,600 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T09:48:10.600884Z", "event_type": "admin.join_socket", "user_id": "admin", "ip_address": "127.0.0.1", "status": "ok", "additional_details": {"s_id": "f6f6b911ceecd84268b8e8b2aea4c787", "sid": "6btf6bU_GfbMoIkZAAAF"}}
2025-05-17 04:48:21,061 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T09:48:21.061935Z", "event_type": "admin.gen_code", "user_id": "admin", "ip_address": "127.0.0.1", "status": "ok", "request_details": {"path": "/api/generate-code", "method": "POST", "user_agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, "additional_details": {"code": "T1XYQCZF", "s_id": "f6f6b911ceecd84268b8e8b2aea4c787"}}
2025-05-17 04:48:32,210 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T09:48:32.210601Z", "event_type": "admin.gen_code", "user_id": "admin", "ip_address": "127.0.0.1", "status": "ok", "request_details": {"path": "/api/generate-code", "method": "POST", "user_agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, "additional_details": {"code": "27Y3MNAR", "s_id": "f6f6b911ceecd84268b8e8b2aea4c787"}}
2025-05-17 04:49:38,029 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T09:49:38.029028Z", "event_type": "user.val_code", "user_id": "N/A", "ip_address": "127.0.0.1", "status": "ok", "request_details": {"path": "/api/validate-code", "method": "POST", "user_agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, "additional_details": {"code": "27Y3MNAR", "admin_s_id": "f6f6b911ceecd84268b8e8b2aea4c787"}}
2025-05-17 04:49:38,284 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T09:49:38.284299Z", "event_type": "user.join_s_socket", "user_id": "User_e35dbd", "ip_address": "127.0.0.1", "status": "ok", "additional_details": {"code": "27Y3MNAR", "admin_s_id": "f6f6b911ceecd84268b8e8b2aea4c787", "admin_user": "admin"}}
2025-05-17 04:49:54,935 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T09:49:54.935348Z", "event_type": "user.submit_number", "user_id": "User_e35dbd", "ip_address": "127.0.0.1", "status": "ok", "additional_details": {"number": "9-10", "admin_s_id": "f6f6b911ceecd84268b8e8b2aea4c787", "wheel": "american"}}
2025-05-17 04:49:57,167 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T09:49:57.167383Z", "event_type": "user.submit_number", "user_id": "User_e35dbd", "ip_address": "127.0.0.1", "status": "ok", "additional_details": {"number": "11", "admin_s_id": "f6f6b911ceecd84268b8e8b2aea4c787", "wheel": "american"}}
2025-05-17 04:50:00,085 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T09:50:00.085935Z", "event_type": "user.submit_number", "user_id": "User_e35dbd", "ip_address": "127.0.0.1", "status": "ok", "additional_details": {"number": "34", "admin_s_id": "f6f6b911ceecd84268b8e8b2aea4c787", "wheel": "american"}}
2025-05-17 04:50:03,564 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T09:50:03.564075Z", "event_type": "user.submit_number", "user_id": "User_e35dbd", "ip_address": "127.0.0.1", "status": "ok", "additional_details": {"number": "36", "admin_s_id": "f6f6b911ceecd84268b8e8b2aea4c787", "wheel": "american"}}
2025-05-17 04:50:10,200 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T09:50:10.199967Z", "event_type": "user.submit_number", "user_id": "User_e35dbd", "ip_address": "127.0.0.1", "status": "ok", "additional_details": {"number": "2-7-23", "admin_s_id": "f6f6b911ceecd84268b8e8b2aea4c787", "wheel": "american"}}
2025-05-17 05:01:58,157 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T10:01:58.157517Z", "event_type": "user.join_socket", "user_id": "N/A", "ip_address": "127.0.0.1", "status": "fail", "additional_details": {"code": "27Y3MNAR", "reason": "Invalid code socket join"}}
2025-05-17 05:02:09,753 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T10:02:09.753593Z", "event_type": "sec.login", "user_id": "admin", "ip_address": "127.0.0.1", "status": "ok", "request_details": {"path": "/admin-login", "method": "POST", "user_agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, "additional_details": {"s_id": "c0e4edea6c45a170c804563cf71bd1ff"}}
2025-05-17 05:02:10,361 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T10:02:10.361523Z", "event_type": "admin.join_socket", "user_id": "admin", "ip_address": "127.0.0.1", "status": "ok", "additional_details": {"s_id": "c0e4edea6c45a170c804563cf71bd1ff", "sid": "oOcWjolOaB1HdHBAAAAD"}}
2025-05-17 05:02:42,440 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T10:02:42.440380Z", "event_type": "admin.gen_code", "user_id": "admin", "ip_address": "127.0.0.1", "status": "ok", "request_details": {"path": "/api/generate-code", "method": "POST", "user_agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, "additional_details": {"code": "HEBPNDK8", "s_id": "c0e4edea6c45a170c804563cf71bd1ff"}}
2025-05-17 05:02:57,755 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T10:02:57.755161Z", "event_type": "user.val_code", "user_id": "N/A", "ip_address": "127.0.0.1", "status": "ok", "request_details": {"path": "/api/validate-code", "method": "POST", "user_agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, "additional_details": {"code": "HEBPNDK8", "admin_s_id": "c0e4edea6c45a170c804563cf71bd1ff"}}
2025-05-17 05:02:58,013 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T10:02:58.013433Z", "event_type": "user.join_s_socket", "user_id": "User_791d06", "ip_address": "127.0.0.1", "status": "ok", "additional_details": {"code": "HEBPNDK8", "admin_s_id": "c0e4edea6c45a170c804563cf71bd1ff", "admin_user": "admin"}}
2025-05-17 05:04:28,783 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T10:04:28.783868Z", "event_type": "user.submit_number", "user_id": "User_791d06", "ip_address": "127.0.0.1", "status": "ok", "additional_details": {"number": "30-21-7-17-21-7-34-20-16-10-26-2-16-31-1-26-34-15-25-10-35-36-16-17-36-36-11-17-6-18-6-24-28-8-0-8-29-24-9-21-23-34-13-13-17-25-7-17-36-15-3-6-17-28-29-14-12-13-15-16-19-33-27-4-7-22-10", "admin_s_id": "c0e4edea6c45a170c804563cf71bd1ff", "wheel": "american"}}
2025-05-17 05:08:54,092 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T10:08:54.092226Z", "event_type": "user.join_socket", "user_id": "N/A", "ip_address": "127.0.0.1", "status": "fail", "additional_details": {"code": "HEBPNDK8", "reason": "Invalid code socket join"}}
2025-05-17 05:10:25,583 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T10:10:25.583735Z", "event_type": "sec.login", "user_id": "admin", "ip_address": "127.0.0.1", "status": "ok", "request_details": {"path": "/admin-login", "method": "POST", "user_agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, "additional_details": {"s_id": "a7176261a22cf69d4a2ec011c8aaddfe"}}
2025-05-17 05:10:25,939 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T10:10:25.938127Z", "event_type": "admin.join_socket", "user_id": "admin", "ip_address": "127.0.0.1", "status": "ok", "additional_details": {"s_id": "a7176261a22cf69d4a2ec011c8aaddfe", "sid": "LV41wp9FItAoviCcAAAF"}}
2025-05-17 05:10:30,548 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T10:10:30.548935Z", "event_type": "admin.gen_code", "user_id": "admin", "ip_address": "127.0.0.1", "status": "ok", "request_details": {"path": "/api/generate-code", "method": "POST", "user_agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, "additional_details": {"code": "2KM7OK94", "s_id": "a7176261a22cf69d4a2ec011c8aaddfe"}}
2025-05-17 05:10:45,114 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T10:10:45.114857Z", "event_type": "user.val_code", "user_id": "N/A", "ip_address": "127.0.0.1", "status": "ok", "request_details": {"path": "/api/validate-code", "method": "POST", "user_agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, "additional_details": {"code": "2KM7OK94", "admin_s_id": "a7176261a22cf69d4a2ec011c8aaddfe"}}
2025-05-17 05:10:45,436 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T10:10:45.436756Z", "event_type": "user.join_s_socket", "user_id": "User_0f73dd", "ip_address": "127.0.0.1", "status": "ok", "additional_details": {"code": "2KM7OK94", "admin_s_id": "a7176261a22cf69d4a2ec011c8aaddfe", "admin_user": "admin"}}
2025-05-17 05:10:57,159 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T10:10:57.159528Z", "event_type": "user.submit_number", "user_id": "User_0f73dd", "ip_address": "127.0.0.1", "status": "ok", "additional_details": {"number": "30-21-7-17-21-7-34-20-16-10-26-2-16-31-1-26-34-15-25-10-35-36-16-17-36-36-11-17-6-18-6-24-28-8-0-8-29-24-9-21-23-34-13-13-17-25-7-17-36-15-3-6-17-28-29-14-12-13-15-16-19-33-27-4-7-22-10-9-24-28-33-11-7-19-17-5-21-35-24-9-34-32-30-16-13-2-00-35-17-8-10-19-7-12-27-19-19", "admin_s_id": "a7176261a22cf69d4a2ec011c8aaddfe", "wheel": "american"}}
2025-05-17 05:11:45,209 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T10:11:45.209565Z", "event_type": "admin.submit_number", "user_id": "admin", "ip_address": "127.0.0.1", "status": "ok", "additional_details": {"number": "15", "s_id": "a7176261a22cf69d4a2ec011c8aaddfe", "wheel": "american"}}
2025-05-17 05:12:14,099 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T10:12:14.099309Z", "event_type": "admin.submit_number", "user_id": "admin", "ip_address": "127.0.0.1", "status": "ok", "additional_details": {"number": "25", "s_id": "a7176261a22cf69d4a2ec011c8aaddfe", "wheel": "american"}}
2025-05-17 05:15:16,920 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T10:15:16.919998Z", "event_type": "admin.change_file_context", "user_id": "admin", "ip_address": "127.0.0.1", "status": "ok", "additional_details": {"new_base": "numbers", "s_id": "a7176261a22cf69d4a2ec011c8aaddfe"}}
2025-05-17 05:15:16,921 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T10:15:16.921081Z", "event_type": "admin.save_history", "user_id": "admin", "ip_address": "N/A", "status": "success", "additional_details": {"file": "session_history/session_admin_20250517_101516_920480_a7176261.txt", "s_id": "a7176261a22cf69d4a2ec011c8aaddfe", "wheel": "american"}}
2025-05-17 05:20:59,196 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T10:20:59.196058Z", "event_type": "user.join_socket", "user_id": "N/A", "ip_address": "127.0.0.1", "status": "fail", "additional_details": {"code": "2KM7OK94", "reason": "Invalid code socket join"}}
2025-05-17 05:21:54,003 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T10:21:54.003328Z", "event_type": "sec.login", "user_id": "admin", "ip_address": "127.0.0.1", "status": "ok", "request_details": {"path": "/admin-login", "method": "POST", "user_agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, "additional_details": {"s_id": "5146123dc26241b5f3e82f7351df0613"}}
2025-05-17 05:21:54,859 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T10:21:54.859222Z", "event_type": "admin.join_socket", "user_id": "admin", "ip_address": "127.0.0.1", "status": "ok", "additional_details": {"s_id": "5146123dc26241b5f3e82f7351df0613", "sid": "SzGoldjxmsLYckrTAAAD"}}
2025-05-17 05:22:10,683 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T10:22:10.683634Z", "event_type": "admin.submit_number", "user_id": "admin", "ip_address": "127.0.0.1", "status": "ok", "additional_details": {"number": "30-21-7-17-21-7-34-20-16-10-26-2-16-31-1-26-34-15-25-10-35-36-16-17-36-36-11-17-6-18-6-24-28-8-0-8-29-24-9-21-23-34-13-13-17-25-7-17-36-15-3-6-17-28-29-14-12-13-15-16-19-33-27-4-7-22-10-9-24-28-33-11-7-19-17-5-21-35-24-9-34-32-30-16-13-2-00-35-17-8-10-19-7-12-27-19-19", "s_id": "5146123dc26241b5f3e82f7351df0613", "wheel": "american"}}
2025-05-17 05:23:52,077 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T10:23:52.077427Z", "event_type": "admin.save_history", "user_id": "admin", "ip_address": "N/A", "status": "success", "additional_details": {"file": "session_history/session_admin_20250517_102352_076744_5146123d.txt", "s_id": "5146123dc26241b5f3e82f7351df0613", "wheel": "american"}}
2025-05-17 05:23:52,077 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T10:23:52.077789Z", "event_type": "admin.save_session", "user_id": "admin", "ip_address": "127.0.0.1", "status": "fail", "additional_details": {"reason": "Failed to save session.", "s_id": "5146123dc26241b5f3e82f7351df0613"}}
2025-05-17 05:24:44,100 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T10:24:44.100373Z", "event_type": "user.join_socket", "user_id": "N/A", "ip_address": "127.0.0.1", "status": "fail", "additional_details": {"code": "2KM7OK94", "reason": "Invalid code socket join"}}
2025-05-17 05:25:31,996 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T10:25:31.996303Z", "event_type": "sec.login", "user_id": "admin", "ip_address": "127.0.0.1", "status": "ok", "request_details": {"path": "/admin-login", "method": "POST", "user_agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, "additional_details": {"s_id": "fe674a8db4472ac36bad005f303550c9"}}
2025-05-17 05:25:32,319 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T10:25:32.319328Z", "event_type": "admin.join_socket", "user_id": "admin", "ip_address": "127.0.0.1", "status": "ok", "additional_details": {"s_id": "fe674a8db4472ac36bad005f303550c9", "sid": "n2txSIKbCHSw2k6aAAAD"}}
2025-05-17 05:25:45,940 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T10:25:45.940853Z", "event_type": "admin.submit_number", "user_id": "admin", "ip_address": "127.0.0.1", "status": "ok", "additional_details": {"number": "30-21-7-17-21-7-34-20-16-10-26-2-16-31-1-26-34-15-25-10-35-36-16-17-36-36-11-17-6-18-6-24-28-8-0-8-29-24-9-21-23-34-13-13-17-25-7-17-36-15-3-6-17-28-29-14-12-13-15-16-19-33-27-4-7-22-10-9-24-28-33-11-7-19-17-5-21-35-24-9-34-32-30-16-13-2-00-35-17-8-10-19-7-12-27-19-19", "s_id": "fe674a8db4472ac36bad005f303550c9", "wheel": "american"}}
2025-05-17 05:26:04,446 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T10:26:04.446845Z", "event_type": "admin.submit_number", "user_id": "admin", "ip_address": "127.0.0.1", "status": "ok", "additional_details": {"number": "33", "s_id": "fe674a8db4472ac36bad005f303550c9", "wheel": "american"}}
2025-05-17 05:26:24,927 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T10:26:24.927047Z", "event_type": "admin.submit_number", "user_id": "admin", "ip_address": "127.0.0.1", "status": "ok", "additional_details": {"number": "9", "s_id": "fe674a8db4472ac36bad005f303550c9", "wheel": "american"}}
2025-05-17 05:28:37,092 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T10:28:37.092262Z", "event_type": "user.join_socket", "user_id": "N/A", "ip_address": "127.0.0.1", "status": "fail", "additional_details": {"code": "2KM7OK94", "reason": "Invalid code socket join"}}
2025-05-17 05:29:04,230 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T10:29:04.230044Z", "event_type": "sec.login", "user_id": "admin", "ip_address": "127.0.0.1", "status": "ok", "request_details": {"path": "/admin-login", "method": "POST", "user_agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, "additional_details": {"s_id": "3d848a165cfb63baa382e705e1f9e160"}}
2025-05-17 05:29:04,592 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T10:29:04.592605Z", "event_type": "admin.join_socket", "user_id": "admin", "ip_address": "127.0.0.1", "status": "ok", "additional_details": {"s_id": "3d848a165cfb63baa382e705e1f9e160", "sid": "A7t-6ZI_52UoLIPwAAAF"}}
2025-05-17 05:29:26,694 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T10:29:26.694761Z", "event_type": "admin.gen_code", "user_id": "admin", "ip_address": "127.0.0.1", "status": "ok", "request_details": {"path": "/api/generate-code", "method": "POST", "user_agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, "additional_details": {"code": "WI1VEE12", "s_id": "3d848a165cfb63baa382e705e1f9e160"}}
2025-05-17 05:29:29,267 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T10:29:29.267594Z", "event_type": "admin.submit_number", "user_id": "admin", "ip_address": "127.0.0.1", "status": "ok", "additional_details": {"number": "30-21-7-17-21-7-34-20-16-10-26-2-16-31-1-26-34-15-25-10-35-36-16-17-36-36-11-17-6-18-6-24-28-8-0-8-29-24-9-21-23-34-13-13-17-25-7-17-36-15-3-6-17-28-29-14-12-13-15-16-19-33-27-4-7-22-10-9-24-28-33-11-7-19-17-5-21-35-24-9-34-32-30-16-13-2-00-35-17-8-10-19-7-12-27-19-19", "s_id": "3d848a165cfb63baa382e705e1f9e160", "wheel": "american"}}
2025-05-17 05:29:43,669 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-17T10:29:43.669399Z", "event_type": "admin.submit_number", "user_id": "admin", "ip_address": "127.0.0.1", "status": "ok", "additional_details": {"number": "33", "s_id": "3d848a165cfb63baa382e705e1f9e160", "wheel": "american"}}
2025-05-19 08:02:08,617 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-19T13:02:08.617296Z", "event_type": "sec.login", "user_id": "admin", "ip_address": "127.0.0.1", "status": "ok", "request_details": {"path": "/admin-login", "method": "POST", "user_agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, "additional_details": {"s_id": "f5e8eb2a9e32667722363d58a3b0380b"}}
2025-05-19 08:02:08,660 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-19T13:02:08.660035Z", "event_type": "admin.join_socket", "user_id": "admin", "ip_address": "127.0.0.1", "status": "ok", "additional_details": {"s_id": "f5e8eb2a9e32667722363d58a3b0380b", "sid": "GWFlTYG1NtX79j-jAAAB"}}
2025-05-19 08:02:20,749 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-19T13:02:20.749648Z", "event_type": "admin.gen_code", "user_id": "admin", "ip_address": "127.0.0.1", "status": "ok", "request_details": {"path": "/api/generate-code", "method": "POST", "user_agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, "additional_details": {"code": "I05HSIU2", "s_id": "f5e8eb2a9e32667722363d58a3b0380b"}}
2025-05-19 12:22:49,286 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-19T17:22:49.286344Z", "event_type": "sec.login", "user_id": "admin", "ip_address": "*************", "status": "ok", "request_details": {"path": "/admin-login", "method": "POST", "user_agent": "Mozilla/5.0 (X11; CrOS x86_64 14541.0.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, "additional_details": {"s_id": "547bc08ba0abf2ed1c09eba8a7412873"}}
2025-05-19 12:22:49,582 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-19T17:22:49.582821Z", "event_type": "admin.join_socket", "user_id": "admin", "ip_address": "*************", "status": "ok", "additional_details": {"s_id": "547bc08ba0abf2ed1c09eba8a7412873", "sid": "59vepUWCOHRnN03jAAAB"}}
2025-05-19 12:24:22,258 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-19T17:24:22.258501Z", "event_type": "sec.login", "user_id": "admin", "ip_address": "*************", "status": "ok", "request_details": {"path": "/admin-login", "method": "POST", "user_agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 18_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.4 Mobile/15E148 Safari/604.1"}, "additional_details": {"s_id": "373de8604d69555ef8661b4bf167ebea"}}
2025-05-19 12:24:22,858 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-19T17:24:22.858022Z", "event_type": "admin.join_socket", "user_id": "admin", "ip_address": "*************", "status": "ok", "additional_details": {"s_id": "373de8604d69555ef8661b4bf167ebea", "sid": "TMQQeW5hFwxW9CYoAAAD"}}
2025-05-19 12:27:15,484 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-19T17:27:15.483999Z", "event_type": "admin.join_socket", "user_id": "admin", "ip_address": "*************", "status": "ok", "additional_details": {"s_id": "373de8604d69555ef8661b4bf167ebea", "sid": "eKqGyfu9PWGeMDT_AAAF"}}
2025-05-19 12:28:38,040 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-19T17:28:38.040613Z", "event_type": "admin.join_socket", "user_id": "admin", "ip_address": "*************", "status": "ok", "additional_details": {"s_id": "547bc08ba0abf2ed1c09eba8a7412873", "sid": "H411EtZ9AdizG8_gAAAH"}}
2025-05-19 12:28:40,862 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-19T17:28:40.862381Z", "event_type": "system.session_cleanup", "user_id": "admin", "ip_address": "N/A", "status": "success", "additional_details": {"s_id": "547bc08ba0abf2ed1c09eba8a7412873", "reason": "logout"}}
2025-05-19 12:28:40,863 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-19T17:28:40.863237Z", "event_type": "sec.logout", "user_id": "admin", "ip_address": "*************", "status": "ok", "request_details": {"path": "/api/logout", "method": "POST", "user_agent": "Mozilla/5.0 (X11; CrOS x86_64 14541.0.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, "additional_details": {"s_id": "547bc08ba0abf2ed1c09eba8a7412873"}}
2025-05-19 13:34:54,894 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-19T18:34:54.894133Z", "event_type": "sec.failed_login", "user_id": "Admin", "ip_address": "127.0.0.1", "status": "fail", "request_details": {"path": "/admin-login", "method": "POST", "user_agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, "additional_details": {"reason": "Invalid creds"}}
2025-05-19 13:35:02,312 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-19T18:35:02.312710Z", "event_type": "sec.failed_login", "user_id": "Admin", "ip_address": "127.0.0.1", "status": "fail", "request_details": {"path": "/admin-login", "method": "POST", "user_agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, "additional_details": {"reason": "Invalid creds"}}
2025-05-19 13:35:04,027 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-19T18:35:04.026976Z", "event_type": "sec.failed_login", "user_id": "Admin", "ip_address": "127.0.0.1", "status": "fail", "request_details": {"path": "/admin-login", "method": "POST", "user_agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, "additional_details": {"reason": "Invalid creds"}}
2025-05-19 13:35:19,975 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-19T18:35:19.975090Z", "event_type": "sec.failed_login", "user_id": "admin1", "ip_address": "127.0.0.1", "status": "fail", "request_details": {"path": "/admin-login", "method": "POST", "user_agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, "additional_details": {"reason": "Invalid creds"}}
2025-05-19 13:35:26,136 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-19T18:35:26.136854Z", "event_type": "sec.failed_login", "user_id": "admin1", "ip_address": "127.0.0.1", "status": "fail", "request_details": {"path": "/admin-login", "method": "POST", "user_agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, "additional_details": {"reason": "Invalid creds"}}
2025-05-19 13:35:28,170 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-19T18:35:28.170647Z", "event_type": "sec.failed_login", "user_id": "admin1", "ip_address": "127.0.0.1", "status": "fail", "request_details": {"path": "/admin-login", "method": "POST", "user_agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, "additional_details": {"reason": "Rate limit"}}
2025-05-19 13:36:28,497 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-19T18:36:28.496996Z", "event_type": "sec.failed_login", "user_id": "Admin", "ip_address": "127.0.0.1", "status": "fail", "request_details": {"path": "/admin-login", "method": "POST", "user_agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, "additional_details": {"reason": "Rate limit"}}
2025-05-19 13:40:47,174 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-19T18:40:47.174822Z", "event_type": "sec.login", "user_id": "admin", "ip_address": "127.0.0.1", "status": "ok", "request_details": {"path": "/admin-login", "method": "POST", "user_agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, "additional_details": {"s_id": "7913c3bf5bba97a8982fb357ecbaaab7"}}
2025-05-19 13:40:47,642 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-19T18:40:47.642358Z", "event_type": "admin.join_socket", "user_id": "admin", "ip_address": "127.0.0.1", "status": "ok", "additional_details": {"s_id": "7913c3bf5bba97a8982fb357ecbaaab7", "sid": "5T27q_bOf6vozJH8AAAB"}}
2025-05-19 13:44:01,989 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-19T18:44:01.989093Z", "event_type": "sec.login", "user_id": "scharway", "ip_address": "127.0.0.1", "status": "ok", "request_details": {"path": "/admin-login", "method": "POST", "user_agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, "additional_details": {"s_id": "215c7036912bea4fa51cb77f70ba2128"}}
2025-05-19 13:44:02,295 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-19T18:44:02.295880Z", "event_type": "admin.join_socket", "user_id": "scharway", "ip_address": "127.0.0.1", "status": "ok", "additional_details": {"s_id": "215c7036912bea4fa51cb77f70ba2128", "sid": "IOD1_Bz3_087wZK_AAAB"}}
2025-05-19 15:58:42,791 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-19T20:58:42.791329Z", "event_type": "system.session_cleanup", "user_id": "scharway", "ip_address": "N/A", "status": "success", "additional_details": {"s_id": "215c7036912bea4fa51cb77f70ba2128", "reason": "periodic_timeout"}}
2025-05-19 23:57:58,118 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-20T04:57:58.118800Z", "event_type": "sec.login", "user_id": "scharway", "ip_address": "127.0.0.1", "status": "ok", "request_details": {"path": "/admin-login", "method": "POST", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, "additional_details": {"s_id": "9000a095d8ebf9b829ac52719d6cb204"}}
2025-05-19 23:57:58,579 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-20T04:57:58.579602Z", "event_type": "admin.join_socket", "user_id": "scharway", "ip_address": "127.0.0.1", "status": "ok", "additional_details": {"s_id": "9000a095d8ebf9b829ac52719d6cb204", "sid": "1Y5zYvtNc53wvUN7AAAB"}}
2025-05-20 01:59:10,897 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-20T06:59:10.897826Z", "event_type": "system.session_cleanup", "user_id": "scharway", "ip_address": "N/A", "status": "success", "additional_details": {"s_id": "9000a095d8ebf9b829ac52719d6cb204", "reason": "periodic_timeout"}}
2025-05-28 11:28:02,599 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-28T16:28:02.599012Z", "event_type": "sec.failed_login", "user_id": "test", "ip_address": "127.0.0.1", "status": "fail", "request_details": {"path": "/admin-login", "method": "POST", "user_agent": "curl/7.81.0"}, "additional_details": {"reason": "Invalid creds"}}
2025-05-28 20:58:22,767 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-29T01:58:22.767696Z", "event_type": "sec.login", "user_id": "scharway", "ip_address": "127.0.0.1", "status": "ok", "request_details": {"path": "/admin-login", "method": "POST", "user_agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, "additional_details": {"s_id": "7ad3abc717a47b3c0b27e503b25b8c6b"}}
2025-05-28 20:58:23,262 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-29T01:58:23.262831Z", "event_type": "admin.join_socket", "user_id": "scharway", "ip_address": "127.0.0.1", "status": "ok", "additional_details": {"s_id": "7ad3abc717a47b3c0b27e503b25b8c6b", "sid": "vmE5-thwHe7kLoZxAAAB"}}
2025-05-28 20:58:36,286 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-29T01:58:36.286065Z", "event_type": "admin.gen_code", "user_id": "scharway", "ip_address": "127.0.0.1", "status": "ok", "request_details": {"path": "/api/generate-code", "method": "POST", "user_agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, "additional_details": {"code": "OT3S9NW9", "s_id": "7ad3abc717a47b3c0b27e503b25b8c6b"}}
2025-05-28 20:58:46,782 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-29T01:58:46.782054Z", "event_type": "user.val_code", "user_id": "N/A", "ip_address": "127.0.0.1", "status": "ok", "request_details": {"path": "/api/validate-code", "method": "POST", "user_agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, "additional_details": {"code": "OT3S9NW9", "admin_s_id": "7ad3abc717a47b3c0b27e503b25b8c6b"}}
2025-05-28 20:58:47,189 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-29T01:58:47.189749Z", "event_type": "user.join_s_socket", "user_id": "User_5e5a97", "ip_address": "127.0.0.1", "status": "ok", "additional_details": {"code": "OT3S9NW9", "admin_s_id": "7ad3abc717a47b3c0b27e503b25b8c6b", "admin_user": "scharway"}}
2025-05-28 20:59:33,126 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-29T01:59:33.126162Z", "event_type": "user.dc_session", "user_id": "User_5e5a97", "ip_address": "127.0.0.1", "status": "ok", "additional_details": {"s_id": "7ad3abc717a47b3c0b27e503b25b8c6b", "sid": "Xniiic-WhHJDa5iaAAAD"}}
2025-05-28 21:00:10,098 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-29T02:00:10.098537Z", "event_type": "sec.login", "user_id": "scharway", "ip_address": "127.0.0.1", "status": "ok", "request_details": {"path": "/admin-login", "method": "POST", "user_agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, "additional_details": {"s_id": "91c897e8411f7b1ba6a6ae465c415f4f"}}
2025-05-28 21:00:10,569 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-29T02:00:10.569104Z", "event_type": "admin.join_socket", "user_id": "scharway", "ip_address": "127.0.0.1", "status": "ok", "additional_details": {"s_id": "91c897e8411f7b1ba6a6ae465c415f4f", "sid": "ckZ_WqzTlO0IfVdJAAAF"}}
2025-05-29 11:51:14,426 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-29T16:51:14.426887Z", "event_type": "sec.login", "user_id": "scharway", "ip_address": "127.0.0.1", "status": "ok", "request_details": {"path": "/admin-login", "method": "POST", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, "additional_details": {"s_id": "f8c899f63924cf112b03be97d10a64c5"}}
2025-05-29 11:51:17,789 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-29T16:51:17.789089Z", "event_type": "admin.join_socket", "user_id": "scharway", "ip_address": "127.0.0.1", "status": "ok", "additional_details": {"s_id": "f8c899f63924cf112b03be97d10a64c5", "sid": "8vFLT9d3ghjcudwQAAAB"}}
2025-05-29 11:51:32,390 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-29T16:51:32.390614Z", "event_type": "admin.gen_code", "user_id": "scharway", "ip_address": "127.0.0.1", "status": "ok", "request_details": {"path": "/api/generate-code", "method": "POST", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, "additional_details": {"code": "W9VRH7EB", "s_id": "f8c899f63924cf112b03be97d10a64c5"}}
2025-05-29 11:52:22,560 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-29T16:52:22.560389Z", "event_type": "admin.submit_number", "user_id": "scharway", "ip_address": "127.0.0.1", "status": "ok", "additional_details": {"number": "13-15-29-1-14-19-7-26-22-00-13-36-12-31-15-10-23-3-27-24-19-28-13-19-6-29-3-1-34-2-20-13-6-25-28-33-9-15-24-12-30-21-22-00-9-11-13-33-35-6-26-26-26-17-23-24-29-7-14-10-36-4-16-36-16-26-22-16-10-10-18-6-8-15-36-9-36-30-20-12-7-11-16-5", "s_id": "f8c899f63924cf112b03be97d10a64c5", "wheel": "american"}}
2025-05-29 11:53:49,373 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-29T16:53:49.373746Z", "event_type": "admin.submit_number", "user_id": "scharway", "ip_address": "127.0.0.1", "status": "ok", "additional_details": {"number": "22", "s_id": "f8c899f63924cf112b03be97d10a64c5", "wheel": "american"}}
2025-05-29 11:54:11,271 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-29T16:54:11.271427Z", "event_type": "admin.submit_number", "user_id": "scharway", "ip_address": "127.0.0.1", "status": "ok", "additional_details": {"number": "23", "s_id": "f8c899f63924cf112b03be97d10a64c5", "wheel": "american"}}
2025-05-29 11:54:45,825 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-29T16:54:45.825348Z", "event_type": "admin.submit_number", "user_id": "scharway", "ip_address": "127.0.0.1", "status": "ok", "additional_details": {"number": "20", "s_id": "f8c899f63924cf112b03be97d10a64c5", "wheel": "american"}}
2025-05-29 12:32:46,307 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-29T17:32:46.307529Z", "event_type": "sec.login", "user_id": "scharway", "ip_address": "127.0.0.1", "status": "ok", "request_details": {"path": "/admin-login", "method": "POST", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, "additional_details": {"s_id": "0f74de00b2083d6f470906bf7629c589"}}
2025-05-29 12:32:47,439 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-29T17:32:47.439465Z", "event_type": "admin.join_socket", "user_id": "scharway", "ip_address": "127.0.0.1", "status": "ok", "additional_details": {"s_id": "0f74de00b2083d6f470906bf7629c589", "sid": "W7SeVU0pnMG5XoT9AAAB"}}
2025-05-29 12:33:12,315 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-29T17:33:12.314935Z", "event_type": "admin.submit_number", "user_id": "scharway", "ip_address": "127.0.0.1", "status": "ok", "additional_details": {"number": "13-15-29-1-14-19-7-26-22-00-13-36-12-31-15-10-23-3-27-24-19-28-13-19-6-29-3-1-34-2-20-13-6-25-28-33-9-15-24-12-30-21-22-00-9-11-13-33-35-6-26-26-26-17-23-24-29-7-14-10-36-4-16-36-16-26-22-16-10-10-18-6-8-15-36-9-36-30-20-12-7-11-16-5", "s_id": "0f74de00b2083d6f470906bf7629c589", "wheel": "american"}}
2025-05-29 14:43:30,720 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-29T19:43:30.720284Z", "event_type": "admin.save_history", "user_id": "scharway", "ip_address": "N/A", "status": "success", "additional_details": {"file": "session_history/session_scharway_20250529_194330_719701_0f74de00.txt", "s_id": "0f74de00b2083d6f470906bf7629c589", "wheel": "american"}}
2025-05-29 14:43:30,720 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-05-29T19:43:30.720513Z", "event_type": "system.session_cleanup", "user_id": "scharway", "ip_address": "N/A", "status": "success", "additional_details": {"s_id": "0f74de00b2083d6f470906bf7629c589", "reason": "periodic_timeout"}}
2025-07-02 22:57:20,393 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-07-03T03:57:20.393616Z", "event_type": "sec.login", "user_id": "scharway", "ip_address": "127.0.0.1", "status": "ok", "request_details": {"path": "/admin-login", "method": "POST", "user_agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, "additional_details": {"s_id": "0134b76b32c22a0580c377b2abbc0cf6"}}
2025-07-02 22:57:20,753 - INFO - audit - audit_logger.log_event:64 - {"timestamp": "2025-07-03T03:57:20.752972Z", "event_type": "admin.join_socket", "user_id": "scharway", "ip_address": "127.0.0.1", "status": "ok", "additional_details": {"s_id": "0134b76b32c22a0580c377b2abbc0cf6", "sid": "cOrGxVir1EmSdFjHAAAB"}}
